import React from 'react';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  containerClassName?: string;
  buttonsContainerClassName?: string;
  prevButtonClassName?: string;
  nextButtonClassName?: string;
  currentPageClassName?: string;
  showPageCount?: boolean; // New boolean prop
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  containerClassName = "flex justify-center mt-4 mb-4",
  buttonsContainerClassName = "flex gap-2",
  prevButtonClassName = "outline outline-offset-2 rounded btn btn-xs mt-1 secondary",
  nextButtonClassName = "outline outline-offset-2 rounded btn btn-xs mt-1",
  currentPageClassName = "border border-sky-600 border-1 rounded px-3 text-lg",
  showPageCount = false // Default to false for backward compatibility
}) => {
  return (
    <div className={containerClassName}>
      <div className={buttonsContainerClassName}>
        <button
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className={prevButtonClassName}
        >
          <FaChevronLeft className="text-xs" />
        </button>
        <div className={currentPageClassName}>
          {showPageCount ? `Page ${currentPage} of ${totalPages}` : currentPage}
        </div>
        <button
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className={nextButtonClassName}
        >
          <FaChevronRight className="text-xs" />
        </button>
      </div>
    </div>
  );
};

export default Pagination;
