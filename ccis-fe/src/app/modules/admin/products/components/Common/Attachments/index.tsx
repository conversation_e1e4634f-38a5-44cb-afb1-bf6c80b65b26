import FileDropzone from "@components/common/FileDropzone";
import Typography from "@components/common/Typography";
import { formatBytes } from "@helpers/file";
import { IAttachment } from "@interface/products.interface";
import { FC, Fragment, useEffect, useState } from "react";
import { FaFile, FaFileAlt, FaSync, FaTrash } from "react-icons/fa";
import { FaCircleCheck, FaFileCirclePlus } from "react-icons/fa6";
import { Watch } from "react-loader-spinner";

type TProps = {
  autoUpload?: boolean;
  label?: string;
  fileType?: string[];
  maxSize?: number;
  attachments: IAttachment[];
  files?: File[];
  setFiles?: (files: Array<File>) => void;
  removeFile?: (index: number) => void;
  uploadFiles?: (files: Array<File>) => void;
  isUploading?: boolean;
  editable?: boolean;
  maxFiles?: number;
  showAddMoreButton?: boolean;
};

const Attachments: FC<TProps> = ({
  autoUpload = true,
  label = "Click or Drag and Drop Files",
  maxSize = 5242880,
  fileType = ["pdf"],
  files = [],
  setFiles,
  attachments = [],
  removeFile,
  editable = true,
  uploadFiles,
  isUploading = false,
  maxFiles = 0,
  showAddMoreButton = true,
}) => {
  const formattedMaxSize = formatBytes(maxSize);
  const [isAllowedSize, setIsAllowedSize] = useState<boolean>(true);
  const [isAllowedType, setIsAllowedType] = useState<boolean>(true);

  const checkFileType = () => {
    const isAllowed = files.find((file) => fileType.includes(file.type));

    if (isAllowed) {
      setIsAllowedType(false);
    } else {
      setIsAllowedType(true);
    }
  };

  const checkFileSize = () => {
    const isExceed = files.find((file) => file.size > maxSize);

    if (isExceed) {
      setIsAllowedSize(false);
    } else {
      setIsAllowedSize(true);
    }
  };

  const handleFile = (acceptedFiles: Array<File>) => {
    setFiles && setFiles(acceptedFiles);
    if (autoUpload) {
      handleUpload(acceptedFiles);
    }
  };

  const handleRemove = (index: number) => {
    removeFile && removeFile(index);
  };

  const handleUpload = (acceptedFiles: Array<File>) => {
    uploadFiles && uploadFiles(acceptedFiles);
  };

  useEffect(() => {
    checkFileType();
    checkFileSize();
  }, [attachments]);

  return (
    <Fragment>
      {(attachments ?? []).length > 0 && (
        <Fragment>
          {!isAllowedSize && (
            <Typography size="sm" className="text-center text-danger">
              Some file size exceeds {formattedMaxSize}. Please select {formattedMaxSize} below file.
            </Typography>
          )}
          {!isAllowedType && (
            <Typography size="sm" className="text-center text-danger">
              Some file type is not allowed. Please select only allowed file type.
            </Typography>
          )}
          {(attachments ?? []).map((attachment, index) => {
            return (
              <div key={`file-attachment${index}`} className="flex flex-1 flex-row items-center justify-between border-[1px] p-2 border-slate-200 rounded-md mt-4">
                <div className="flex flex-row items-center ml-4">
                  {(attachments ?? [])[index]?.id === undefined && <FaSync className="text-gray mr-4" size={20} />}
                  {(attachments ?? [])[index]?.id !== undefined && <FaCircleCheck className="text-success mr-4" size={20} />}
                  <FaFileAlt size={30} className="mr-2" />
                  {!attachment.size && (
                    <div className="flex flex-col">
                      <Typography size="md" className={`${attachment.file && attachment?.file?.size > maxSize ? "!text-danger" : "!text-primary"}`}>
                        Attachment {index + 1} : {attachment.file && attachment.file.name}
                      </Typography>
                      <Typography size="xs" className={`${attachment.file && attachment?.file?.size > maxSize ? "!text-danger" : "!text-primary"}`}>
                        {formatBytes(attachment.file ? attachment.file.size : 0)}
                      </Typography>
                    </div>
                  )}
                  {attachment.size && (
                    <div className="flex flex-col">
                      <Typography size="md" className={`${Number(attachment.size) > maxSize ? "!text-danger" : "!text-primary"}`}>
                        Attachment {index + 1} : {attachment.label}
                      </Typography>
                      <Typography size="xs" className={`${Number(attachment.size) > maxSize ? "!text-danger" : "!text-primary"}`}>
                        {formatBytes(Number(attachment.size))}
                      </Typography>
                    </div>
                  )}
                </div>
                {editable && !isUploading && (
                  <button type="button" className="btn-ghost text-slate mr-4 p-2 rounded-full" onClick={() => handleRemove(index)}>
                    <FaTrash />
                  </button>
                )}
                {isUploading && (
                  <div className="flex flex-row items-center mr-4">
                    <Watch height={20} width={20} color="#26A844" />
                  </div>
                )}
              </div>
            );
          })}
        </Fragment>
      )}
      {editable && (
        <Fragment>
          <FileDropzone setFiles={handleFile} maxFiles={maxFiles} height={attachments.map((f) => f.file).length === 0 ? 200 : 20}>
            <div className="flex flex-col flex-1">
              {(attachments ?? []).length === 0 && (
                <div className="flex flex-1 flex-col items-center">
                  <FaFile size={30} className="mb-4" />
                  <Typography>{label}</Typography>
                  <Typography className="text-slate-400">
                    {fileType ? fileType.toString().toUpperCase() : "PDF, PNG, JPEG"} (Max {formattedMaxSize}) / Max files: {maxFiles} {maxFiles > 1 ? "files" : "file"}
                  </Typography>
                </div>
              )}
              {(attachments ?? []).length > 0 && !isUploading && showAddMoreButton && (!maxFiles || attachments.length < maxFiles) && (
                <div className="flex flex-1 flex-col items-center justify-center pt-1 space-x-2 mt-4">
                  <button type="button" className="btn btn-sm btn-success text-white mt-2">
                    Add more
                    <FaFileCirclePlus />
                  </button>
                </div>
              )}
              {isUploading && (
                <div className="flex flex-1 flex-col items-center justify-center pt-1 space-x-2 mt-4">
                  <div className="flex flex-1 flex-row items-center space-x-0 justify-center mt-4">
                    <Typography>Uploading attachments...</Typography>
                    <Watch height={20} width={30} />
                  </div>
                </div>
              )}
            </div>
          </FileDropzone>
        </Fragment>
      )}
    </Fragment>
  );
};

export default Attachments;
