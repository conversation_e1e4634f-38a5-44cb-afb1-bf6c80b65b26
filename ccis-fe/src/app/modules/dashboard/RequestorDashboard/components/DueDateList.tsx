
import React from "react";
import { HiOutlineClock } from "react-icons/hi2";
import { useNavigate } from "react-router-dom";
import { getRolePathWithId } from "@helpers/navigatorHelper";
import { SharedRoutes } from "@enums/shared-routes";

//We get the info regarding the ticket first before we can proceed to parse the information
import { useTicketActions } from "@state/reducer/departmental-ticketing";
import { useEffect } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@state/store";
import { IDueTicketInterface } from "@interface/departmental-ticketing-interface";
import { formatDateToMonthYear} from "@helpers/date";
import Loader from "@components/Loader";

//For mobile devices
import useIsMobile from "@hooks/useIsMobile";

const DueDateList: React.FC = () => {
  const navigate = useNavigate();
  
  const { getDueTicket } = useTicketActions();
  const dueTicket = useSelector((state: RootState) => state.departmentalTicketing?.getDueTicket?.data?.data || []);
  const me = useSelector((state: RootState) => state.auth.user.data);
  const loading = useSelector((state: RootState) => state.departmentalTicketing?.getDueTicket?.loading);
  const error = useSelector((state: RootState) => state.departmentalTicketing?.getDueTicket?.error);
  const isManager = useSelector((state: RootState) => state.managerIds.managerIds || []);
  const isVicePresident = useSelector((state: RootState) => state.globalSettings?.getGlobalSettings?.data?.data?.find((setting: any) => setting.key === "vice_president_user_ids")?.value.includes(me?.id));
  const isPresident = useSelector((state: RootState) => state.globalSettings?.getGlobalSettings?.data?.data?.find((setting: any) => setting.key === "president_user_ids")?.value.includes(me?.id));
  const isMobile = useIsMobile();
  let condition = ""
  //Display info depending on the user's role
  if(isManager){
    condition = `toDepartmentId[eq]=${me?.departmentId}`;
  }else if(isVicePresident||isPresident){
    condition = ``;
  }else{
    condition = `createdBy.id[eq]=${me?.id}`;
  }

  useEffect(() => {
    getDueTicket({
      params: {
        relations:`ticketAssignees.user|fromDepartment`,
        condition: condition,
      }
    });
  }, []);

  const dueTickets = dueTicket.filter((ticket: IDueTicketInterface) => {
    return (((ticket.extensionDate || ticket.expectedCompletionDate) < new Date().toISOString()));
  });

  const LoadingErrorView = () => {
    if (loading) {
      return (
        <div className="flex flex-1 w-full items-center justify-center">
          <Loader />
        </div>
      );
    } else if (error) {
      return (
        <div className="flex flex-1 w-full items-center justify-center">
          <div className="p-4 text-red-500 text-center">
            <p>Failed to load tasks. Please try again.</p>
          </div>
        </div>
      );
    } else if (!dueTickets.data || dueTickets.length === 0) {
      return (
        <div className="flex flex-1 w-full items-center justify-center">
          <div className="p-4 text-gray-500 text-center">
            <p>No tasks past due found.</p>
          </div>
        </div>
      );
    } else{
      return null;
    }
  }

  return (
    <div className="p-4 bg-white shadow rounded-md mt-4">
      <h2 className="text-lg font-semibold mb-4">Past Due Date</h2>
      <div className="flex flex-col h-72 overflow-y-auto my-5  border-y border-slate-300 ">
        {(loading || error || !dueTickets || dueTickets.length === 0) ? (
          <LoadingErrorView />
        ) : (
          dueTickets.map((ticket: IDueTicketInterface) => {
            return (
              <div 
                key={ticket.id} 
                className="my-1 w-full border border-slate-300 rounded p-2 cursor-pointer hover:bg-gray-50 hover:border-gray-400 transition-colors"
                onClick={() => navigate(getRolePathWithId(SharedRoutes.REQUEST_FORM, ticket.id?.toString() || ""))}
              >
                <div className="flex justify-between">
                  <div className="flex gap-2 justify-start">
                    <div>
                      <HiOutlineClock className="text-red-600 text-2xl my-4 mx-2"/>
                    </div>
                    
                    <div className="p-2">
                      <p className={`text-sm text-slate-600 text-ellipsis overflow-hidden py-2 ${isMobile ? 'w-40' : 'w-80'}`}>{ticket.subject}</p>
                      <div className="flex gap-2 text-xs text-slate-400">
                        <p>Ticket {ticket.id}</p>
                        <p>{ticket.fromDepartment?.description || "N/A"}</p>
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-end mt-4">
                    <p className="text-xs text-red-400">Due: {formatDateToMonthYear((ticket.expectedCompletionDate > (ticket.extensionDate || "") ? ticket.expectedCompletionDate : ticket.extensionDate))}</p>
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>
    </div>
  );
};

export default DueDateList;
