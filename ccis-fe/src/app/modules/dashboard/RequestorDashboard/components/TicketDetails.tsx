import Typography from "@components/common/Typography";
import React, { useState } from "react";
import CloseTicketModal from "./modals/CloseTicketModal";
import { useSelector } from "react-redux";
import { RootState } from "@state/store";
import ExtensionDateModal from "./modals/ExtensionDateModal";
import { StatusBadge } from "./common/status-badge";
import { RequestStatus } from "@enums/ticket-status";
import VPSApprovalModal from "./modals/VPSApprovalModal";
import { toast } from "react-toastify";
import { TicketDetailsProps } from "@interface/ticket-details.interface";
import RejectTicketModal from "./modals/RejectTicketModal";
import Button from "@components/common/Button";
import { ITicketAssigneeRoleConstant } from "@interface/ticket-assignee-roles.interface";
import ForApprovalModal from "./modals/ForApprovalModal";
import AssignUserModal from "./modals/AssignUserModal";
import { useTicketActions } from "@state/reducer/departmental-ticketing";
import { ITicketAssignee } from "@interface/departmental-ticketing-interface";
import { SYSTEM_SETTINGS_IDS } from "@constants/global-constant-value";
import { updateTicketApprovalService } from "@services/departmental-ticketing/departmental-ticketing.service";
import { confirmApproval, confirmVPsApproval, showApprovalSuccess, showProcessingToast, showSuccessToast, showTicketReceived } from "./prompts/DepartmentalTicketingPrompts";

const TicketDetails: React.FC<TicketDetailsProps> = ({
  ticketId,
  priorityLevel,
  closureStatus,
  status,
  createdAt,
  expectedCompletionDate,
  toDepartmentName,
  extensionDate,
  handleFetchTicketByID,
  toDepartmentId,
  ticketAssignees,
  createdBy
}) => {
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [isExtensionModalOpen, setIsExtensionModalOpen] = useState<boolean>(false);
  const [vpsApprovalModalOpen, setVpsApprovalModalOpen] = useState<boolean>(false);
  const [assignUserModalOpen, setAssignUserModalOpen] = useState<boolean>(false);
  const [modalTitle, setModalTitle] = useState<string>("");
  const user = useSelector((state: RootState) => state?.auth?.user?.data);
  const [isRejectModalOpen, setIsRejectModalOpen] = useState<boolean>(false);
  const currentUserDepartmentId = user?.department?.id;
  const { putUpdateTicketStatus, postUpdateTicketApproval } = useTicketActions();
  const vicePresidentIds = useSelector((state: RootState) => state?.globalSettings?.getGlobalSettings?.data?.data?.find((setting: any) => setting.key === SYSTEM_SETTINGS_IDS.VICE_PRESIDENT_USER_ID)?.value);
  const [forApprovalModalOpen, setForApprovalModalOpen] = useState<boolean>(false);
  const [modalAction, setModalAction] = useState(null); // Track 'reject' or 'reconsider'
  const [approvalContext, setApprovalContext] = useState<string>("");
 
  const [remarks, setRemarks] = useState<string>("");


  const assignee = ticketAssignees?.find(assignment => assignment.role === ITicketAssigneeRoleConstant.ticketAssignee);

 
  
  // Extract firstname and lastname from the user object if assignee exists
  const assignedToFirstname = assignee?.user?.firstname || '';
  const assignedToLastname = assignee?.user?.lastname || '';

  const hasAssignee = ticketAssignees?.some(assignment => assignment.role === ITicketAssigneeRoleConstant.ticketAssignee);

  const openModal = (action: any) => {
    setModalAction(action);
    setIsRejectModalOpen(true);
  };

  const closeModal = () => {
    setIsRejectModalOpen(false);
    setModalAction(null); // Reset action
  };
  const modalProps = modalAction === "reconsider" ? { title: "Reconsider", placeholder: "Enter remarks" } : { title: "Reject Request", placeholder: "Enter remarks" };
  // Helper to format dates or return fallback
  const formatDate = (date: string | null) => {
    return date ? new Date(date).toLocaleDateString("en-US") : "----";
  };

  // Handle approval update feedback

  const handleReject = async (remarks: string) => {
    const status = modalProps.title === "Reconsider" ? RequestStatus.Reconsidered : RequestStatus.Rejected;
    // const successMessage = modalProps.title === "Reconsider" ? "Ticket set to pending successfully" : "Ticket rejected successfully";
    
    try {
      // Common payload for postUpdateTicketApproval
      const payload = {
        status,
        remarks,
      };

      // Check if user is president, vice president, or topman signatory
      const isTopManSignatory = !!findApproveRejectTopmanSignatory(ticketAssignees || [], user?.id);
      const isReceivingDepartment = !!findApproveRejectReceivingDepartment(ticketAssignees || [], user?.id, createdBy || null);
      const isPresident = !!findApproveRejectApprovalPresident(ticketAssignees || [], user?.id);

      if (shouldShowAcknowledgeButton(ticketAssignees || [], user?.id)) {
        // Try nako if magamit pa
        // await postUpdateTicketApproval({
        //     ticketId,
        //     payload: {
        //       status: RequestStatus.ForReconsideration,
        //       remarks,
        //     }
        // });
        putUpdateTicketStatus({ id: ticketId, status: RequestStatus.Pending });
        // Refresh data after successful operations
        // await handleFetchTicketByID();
      }

      if ( isTopManSignatory || isReceivingDepartment || isPresident) {
        await postUpdateTicketApproval({
          ticketId,
          payload,
        });

        // Update ticket status to Rejected for  vice president, or topman signatory
        if (isTopManSignatory || isReceivingDepartment) {
        await putUpdateTicketStatus({ id: ticketId, status: RequestStatus.Rejected });
          
        }
        // Refresh data after successful operations
        // await handleFetchTicketByID();
      } else {
        // For other users, only update ticket approval
        await postUpdateTicketApproval({
          ticketId,
          payload,
        });
        // Refresh data after successful operations
      }
      await handleFetchTicketByID();


      // toast.success(successMessage);
      closeModal(); // Close modal after successful action
    } catch (error: any) {
      toast.error(`Failed to process ticket: ${error.message}`);
    }
  };


  const handleApprovalSubmit = async (remarks: string) => {
    try {
      if (vicePresidentIds?.includes(user?.id)) {
        await handleVicePresidentForApproval(remarks);
      } else if (shouldShowForApprovalReceivingDepartmentButton(ticketAssignees || [], user?.id)) {
        setVpsApprovalModalOpen(true);
        setRemarks(remarks);
      } else {
        await postUpdateTicketApproval({
        ticketId,
        payload: {
          status: RequestStatus.Rejected,
          remarks,
        }
      });
      }
     
      putUpdateTicketStatus({ id: ticketId, status: RequestStatus.Pending });
    } catch (error: any) {
      showApprovalSuccess("Failed to submit approval request", `${error.message}`, "error");
      toast.error(`Failed to submit approval request: ${error.message}`);
    }
  };

  const openModalWithTitle = (title: string) => {
    setModalTitle(title);
    setIsModalOpen(true);
  };

  const message = vicePresidentIds?.includes(user?.id) ? "Are you sure you want to approve this reconsideration?" : "Are you sure you want to approve this request?";

  const topManPayload =
    vicePresidentIds?.includes(user?.id) || toDepartmentId === currentUserDepartmentId ? { status: RequestStatus.Approved } : { status: RequestStatus.Approved, hasTopManApproval: false };

  const isUserTicketAssignee = (ticketAssignees: ITicketAssignee[], userId: number): boolean => {
    if (!ticketAssignees || !userId) {
      return false;
    }
    
    return ticketAssignees.some((assignee: ITicketAssignee) => 
      assignee?.role === ITicketAssigneeRoleConstant.ticketAssignee && assignee?.user?.id === userId
    );
  };


  function findApproveRejectRequestManager(ticketAssignees: any, currentUserId: number) {
    return ticketAssignees.find((assignee: any) => assignee.userId === currentUserId && 
    assignee.role === ITicketAssigneeRoleConstant.requestorManager && 
    assignee.status === RequestStatus.ForApproval );
  }

   function findApproveRejectTopmanSignatory(ticketAssignees: any, currentUserId: number) {
    return ticketAssignees.find((assignee: any) => assignee.userId === currentUserId && assignee.role === ITicketAssigneeRoleConstant.topManApprover && assignee.status === RequestStatus.ForApproval);
  }

  function findApproveRejectReceivingDepartment(ticketAssignees: any, currentUserId: number, createdBy: number | null) {

    const showifTopManApproved = ticketAssignees.find((assignee: any) => assignee.role === ITicketAssigneeRoleConstant.topManApprover && assignee.status === RequestStatus.Approved);
    const showifRequestorManagerApproved = ticketAssignees.find((assignee: any) => assignee.role === ITicketAssigneeRoleConstant.requestorManager && assignee.status === RequestStatus.Approved);
    const showIfUserIsReceivingDepartment = ticketAssignees.find((assignee: any) => assignee.userId === currentUserId && assignee.role === ITicketAssigneeRoleConstant.receivingDepartment && assignee.status === RequestStatus.ForApproval);
  
      // 1st condition: Check if current user is in ticketAssignees with RECEIVING_DEPARTMENT role and FOR_APPROVAL status
    const userIsReceivingDeptForApproval = ticketAssignees.some((assignee: any) => 
      assignee.userId === currentUserId && 
      assignee.role === ITicketAssigneeRoleConstant.receivingDepartment && 
      assignee.status === RequestStatus.ForApproval
    );
    
    // 2nd condition: Check if there's a VICE_PRESIDENT with userId === createdBy
    const hasVicePresidentCreatedBy = ticketAssignees.some((assignee: any) => 
      assignee.role === ITicketAssigneeRoleConstant.vicePresident && 
      assignee.userId === createdBy
    );

    const originalCondition = showifRequestorManagerApproved && 
      showIfUserIsReceivingDepartment && 
      (showifTopManApproved || !ticketAssignees.some((assignee: any) => 
      assignee.role === ITicketAssigneeRoleConstant.topManApprover));
  
    const newConditions = userIsReceivingDeptForApproval && hasVicePresidentCreatedBy;
  
    return originalCondition || newConditions;
    // return showifRequestorManagerApproved && showIfUserIsReceivingDepartment && (showifTopManApproved || !ticketAssignees.some((assignee: any) => assignee.role === ITicketAssigneeRoleConstant.topManApprover));
  }
  function findAssignExtendReceivingDepartment(ticketAssignees: any, currentUserId: number) {
    return ticketAssignees.find((assignee: any) => assignee.userId === currentUserId && assignee.role === ITicketAssigneeRoleConstant.receivingDepartment && assignee.status !== RequestStatus.ForApproval);
  }


  //For Future Use 
  // function findCloseTicketRequestorManager(ticketAssignees: any, currentUserId: number) {
  //   return ticketAssignees.find((assignee: any) => assignee.userId === currentUserId && assignee.role === ITicketAssigneeRoleConstant.requestorManager);
  // }

  function showWhereToForward(ticketAssignees: any) {
    return ticketAssignees.find((assignee: any) => assignee.role === ITicketAssigneeRoleConstant.receivingDepartment &&
    assignee.status === ITicketAssigneeRoleConstant.forwarded);
  }

  function findApproveRejectApprovalPresident(ticketAssignees: any, currentUserId: number) {
    return ticketAssignees.find((assignee: any) => 
      assignee.userId === currentUserId && assignee.role === ITicketAssigneeRoleConstant.president && 
      assignee.status === RequestStatus.ForApproval 
  )};

  function shouldShowAcknowledgeButton(ticketAssignees: any[], currentUserId: number): boolean {
  // Check if current user is REQUESTOR_MANAGER
    const isRequestorManager = ticketAssignees.some((assignee: any) => 
      assignee.userId === currentUserId && assignee.role === ITicketAssigneeRoleConstant.requestorManager && assignee.status !== RequestStatus.Rejected
    );
    
    // Check if there's a RECEIVING_DEPARTMENT with REJECTED status
    const hasRejectedReceivingDept = ticketAssignees.some((assignee: any) => 
      assignee.role === ITicketAssigneeRoleConstant.receivingDepartment && assignee.status === RequestStatus.Rejected
    );
    
    const ticketClosureStatus = status !== RequestStatus.Resolved && status !== RequestStatus.Unresolved;
    
    // Button should only show when both conditions are true
    return isRequestorManager && hasRejectedReceivingDept && ticketClosureStatus;
  }

  function shouldShowForApprovalReceivingDepartmentButton(ticketAssignees: any[], currentUserId: number): boolean {
    // 1st condition: Check if current user exists in ticketAssignees with RECEIVING_DEPARTMENT role
    const isReceivingDepartment = ticketAssignees.some((assignee: any) => 
      assignee.userId === currentUserId && assignee.role === ITicketAssigneeRoleConstant.receivingDepartment
    );

    // 2nd condition: Check if there's a REQUESTOR_MANAGER with FOR_RECONSIDERATION status
    const hasRequestorManagerForReconsideration = ticketAssignees.some((assignee: any) => 
      assignee.role === ITicketAssigneeRoleConstant.requestorManager && 
      assignee.status === RequestStatus.Reconsidered
    );

    // 3rd condition: Check if there's a REQUESTOR_MANAGER with FOR_VICE_PRESIDENT_APPROVAL status
    const vicePresidentAlreadySentForApproval = ticketAssignees.some((assignee: any) => 
      assignee.role === ITicketAssigneeRoleConstant.requestorManager && 
      assignee.status === RequestStatus.ForVicePresidentApproval
    );

    // 4th condition: Ensure no RECEIVING_DEPARTMENT assignee has FOR_VICE_PRESIDENT_APPROVAL status
    const noReceivingDepartmentForVicePresidentApproval = !ticketAssignees.some((assignee: any) => 
      assignee.role === ITicketAssigneeRoleConstant.receivingDepartment && 
      assignee.status === RequestStatus.ForVicePresidentApproval
    );
    // 5th condition: Ensure neither VICE_PRESIDENT nor PRESIDENT has APPROVED status
    const noVicePresidentOrPresidentApproved = !ticketAssignees.some((assignee: any) =>
      (assignee.role === ITicketAssigneeRoleConstant.vicePresident || 
      assignee.role === ITicketAssigneeRoleConstant.president) && 
      assignee.status === RequestStatus.Approved
    );
    // Button should show when:
    // - User is in RECEIVING_DEPARTMENT
    // AND
    // - Either REQUESTOR_MANAGER has FOR_RECONSIDERATION status OR REQUESTOR_MANAGER has FOR_VICE_PRESIDENT_APPROVAL status
    // AND
    // - No RECEIVING_DEPARTMENT assignee has FOR_VICE_PRESIDENT_APPROVAL status
    return (
      isReceivingDepartment && 
      (hasRequestorManagerForReconsideration || vicePresidentAlreadySentForApproval) && 
      noReceivingDepartmentForVicePresidentApproval && noVicePresidentOrPresidentApproved
    );
  }

   function shouldShowForApprovalVicePresidentButton(ticketAssignees: any[], currentUserId: number): boolean {
    // 1st condition: Check if current user exists in ticketAssignees
    const isVicePresident = ticketAssignees.some((assignee: any) => 
      assignee.userId === currentUserId &&
      assignee.role === ITicketAssigneeRoleConstant.vicePresident
    );
    
    // 2nd condition: Check if there's a REQUESTOR_MANAGER with FOR_RECONSIDERATION status
    const hasRequestorManagerForReconsideration = ticketAssignees.some((assignee: any) => 
      assignee.role === ITicketAssigneeRoleConstant.vicePresident && 
      assignee.status === RequestStatus.ForApproval
    );

    // 3rd condition: Only show this button if Receiving Department still have status of "FOR_VICE_PRESIDENT_APPROVAL"
    const receivingDepartmentStatusForVicePresidentApproval = ticketAssignees.some((assignee: any) =>
      assignee.role === ITicketAssigneeRoleConstant.receivingDepartment &&
      assignee.status === RequestStatus.ForVicePresidentApproval
    );
    
    // Button should only show when both conditions are true
    return isVicePresident && hasRequestorManagerForReconsideration && receivingDepartmentStatusForVicePresidentApproval;
  }

  const handleVicePresidentApproval = async () => {
    const result = await confirmApproval("Confirmation", "Are you sure you want to approve this ticket?");
    if (result.isConfirmed) {
      try {
        // Update the VP's approval status to APPROVED
        await postUpdateTicketApproval({
          ticketId,
          payload: {
            status: RequestStatus.Approved,
          },
        });
        
        // Update overall ticket status to APPROVED
        await putUpdateTicketStatus({ 
          id: Number(ticketId), 
          status: RequestStatus.Approved 
        });
        // Refresh data after successful operations
        await handleFetchTicketByID();
        
        toast.success("Ticket approved successfully by Vice President");
      } catch (error: any) {
        toast.error(`Failed to approve ticket: ${error.message}`);
      }
    }
  };
  const handleVicePresidentForApproval = async (remarks: string) => {
    try {
      // Send for higher approval (President)
      await postUpdateTicketApproval({
        ticketId,
        payload: {
          status: RequestStatus.Rejected,
          remarks,
        }
      });
      
      // Update overall ticket status to FOR_PRESIDENT_APPROVAL
      // FOR FUTURE USE DLI SA NAKO IWALA
      // await putUpdateTicketStatus({ 
      //   id: Number(ticketId), 
      //   status: RequestStatus.Rejected
      // });
      // Refresh data after successful operations
      await handleFetchTicketByID();
      
      toast.success("Ticket forwarded for President approval");
    } catch (error: any) {
      toast.error(`Failed to forward for approval: ${error.message}`);
    }
  };

  const getApprovalDescription = (context: string) => {
    if (context === 'fromDepartment') {
      return "This ticket request is for approval to the Vice President. Please provide your remarks below.";
    } else if (context === 'fromVicePresident') {
      return "This ticket request is for approval to the President. Please provide your remarks below.";
    }
    return "This ticket request is for approval. Please provide your remarks below."; // Fallback
  };
  
  // Future use
  // function shouldShowApprovalButton(ticketAssignees: any[], currentUserId: number): boolean {
  //   // 1st condition: Check if current user is a REQUESTOR
  //   const isCurrentUserRequestor = ticketAssignees.some((assignee: any) => 
  //     assignee.role === ITicketAssigneeRoleConstant.requestorManager && 
  //     assignee.user.id === currentUserId
  //   );
    
  //   // 2nd condition: Check if there's a RECEIVING_DEPARTMENT with FOR_APPROVAL status
  //   const hasReceivingDepartmentForApproval = ticketAssignees.some((assignee: any) => 
  //     assignee.role === ITicketAssigneeRoleConstant.receivingDepartment && 
  //     assignee.status === RequestStatus.ForApproval
  //   );

  //   // 3rd condition: Check if there's NO TOP_MAN_APPROVER (button should NOT show if this role exists)
  //   const hasTopManApprover = ticketAssignees.some((assignee: any) => 
  //     assignee.role === ITicketAssigneeRoleConstant.topManApprover
  //   );

  //   const hasRequestor = ticketAssignees.some((assignee: any) => 
  //     assignee.role === ITicketAssigneeRoleConstant.requestor
  //   );
    
  //   // Button should show when:
  //   // - Current user is a requestor AND
  //   // - There's a receiving department with FOR_APPROVAL status AND
  //   // - There's NO top man approver
  //   return isCurrentUserRequestor && hasReceivingDepartmentForApproval && !hasTopManApprover && !hasRequestor;
  // }

  function doNotDisplayRejectButtonIfCreatorTicketIsVP(ticketAssignees: any[], currentUserId: number, createdBy: number): boolean {
    // 1st condition: Check if current user is a Receiving Department
    const isCurrentUserReceivingDepartment = ticketAssignees.some((assignee: any) => 
      assignee.role === ITicketAssigneeRoleConstant.receivingDepartment && 
      assignee.user.id === currentUserId
    );

    // 2nd condition: Check if the creator of the ticket is a Vice President
    const isTicketCreatorVicePresident = ticketAssignees.some((assignee: any) => 
      assignee.role === ITicketAssigneeRoleConstant.vicePresident && 
      assignee.userId === createdBy
    );

    return isCurrentUserReceivingDepartment && isTicketCreatorVicePresident;
  };

 

  return (
    <div className="border-none">
      <Typography className="text-sm font-poppins-semibold mb-2">TICKET DETAILS</Typography>
      <div className="p-2 border-b border-slate-300">
        <div className="grid grid-cols-3 items-center mb-2">
          <p className="text-xs">Priority Level</p>
          <div className="col-span-2">
            <StatusBadge status={priorityLevel as RequestStatus} className="text-xs p-2" />
          </div>
        </div>
        <div className="grid grid-cols-3 items-center mb-2">
          <p className="text-xs">Closure Status</p>
          <div className="col-span-2">
            <StatusBadge status={closureStatus} className="text-xs p-2" />
          </div>
        </div>
        <div className="grid grid-cols-3 items-center mb-2">
          <p className="text-xs">Ticket Status</p>
          <div className="col-span-2">
            <StatusBadge status={status} className="text-xs p-2" />
          </div>
        </div>
        <div className="grid grid-cols-3 items-center mb-2">
          <p className="text-xs">Date Created</p>
          <div className="col-span-2">
            <Typography className="text-xs p-2">{formatDate(createdAt)}</Typography>
          </div>
        </div>
        <div className="grid grid-cols-3 items-center mb-2">
          <p className="text-xs">Expected Resolution Date</p>
          <div className="col-span-2">
            <Typography className="text-xs p-2">{formatDate(expectedCompletionDate)}</Typography>
          </div>
        </div>
      </div>
      {toDepartmentId !== null && (
        <div className="p-2">
          <Typography className="text-sm font-poppins-semibold mb-2">RECEIVING DEPARTMENT</Typography>
          <div className="grid grid-cols-3 items-center mb-2">
            <p className="text-xs">Department</p>
            <div className="col-span-2">
              <Typography className="text-xs p-2">{toDepartmentName || "N/A"}</Typography>
            </div>
          </div>
          <div className="grid grid-cols-3 items-center mb-2">
            <p className="text-xs">Assigned To</p>
            <div className="col-span-2">
              <Typography className="text-xs p-2">{assignedToFirstname && assignedToLastname ? `${assignedToFirstname} ${assignedToLastname}`.trim() : "Unassigned"}</Typography>
            </div>
          </div>
          <div className="grid grid-cols-3 items-center mb-2">
            <p className="text-xs">Extension Date</p>
            <div className="col-span-2">
              <Typography className="text-xs p-2">{formatDate(extensionDate)}</Typography>
            </div>
          </div>
        </div>
      )}
      <div className="p-2">
        {showWhereToForward(ticketAssignees || []) && (
          <>
            <Typography className="divider italic text-sm text-custom-gray mb-2">Forwarded to</Typography>
            <div className="grid grid-cols-3 items-center mb-2">
              <p className="text-xs">Department</p>
              <div className="col-span-2">
                <Typography className="text-xs p-2">{toDepartmentName || "N/A"}</Typography>
              </div>
            </div>
          </>
        )}
      </div>
      {status === RequestStatus.Assigned && isUserTicketAssignee (ticketAssignees || [], user?.id) && (
            <Button
              classNames="bg-sky-600 hover:bg-sky-700 text-white text-xs mt-4 w-full"
              onClick={async () => {
                const confirmApprovalPrompt = await confirmApproval(
                  "Confirmation",
                  "Are you sure you want to receive this request?",
                  undefined, // onApprove callback (if needed)
                  "question", // icon (optional)
                  "Yes" // confirmButtonText set to "Yes"
                );
                if (confirmApprovalPrompt.isConfirmed) {
                  try {
                    await putUpdateTicketStatus({ id: ticketId, status: RequestStatus.InProgress }); // Remove await here
                    handleFetchTicketByID(); // Refresh data after successful operations
                    showTicketReceived();
                    
                  } catch (error) {
                    toast.error("Failed to receive ticket");
                  }
                  handleFetchTicketByID(); // Ensure data is refreshed
                }
              }}
            >
              Receive Ticket
            </Button>
          )}
          {/* For Future use */}
        {/* {shouldShowApprovalButton(ticketAssignees || [], user?.id) && (
          <Button 
            classNames="bg-sky-600 hover:bg-sky-700 text-white text-xs mt-4 w-full"
            onClick={async () => {
              const vpResult = await confirmVPsApproval();
              if (vpResult.isConfirmed) {
                setVpsApprovalModalOpen(true);
                // Let the modal handle the refresh after it closes
                return; // Exit early to prevent the handleFetchTicketByID call below
              }
            }}
          >
            Approve
          </Button>
        )} */}
          

            
        { closureStatus !== RequestStatus.Resolved &&
          closureStatus !== RequestStatus.Unresolved &&
          (createdBy === user?.id) && (
            <Button classNames="bg-sky-600 hover:bg-sky-700 text-white text-xs mt-4 w-full" onClick={() => openModalWithTitle("Close Ticket")}>
              Close Ticket
            </Button>
          )
        }
    
      {status === RequestStatus.InProgress && isUserTicketAssignee(ticketAssignees || [], user?.id) && (
        <div className="flex justify-end gap-2">
          <Button classNames="bg-sky-600 hover:bg-sky-700 text-white text-xs mt-4 w-full" onClick={() => openModalWithTitle("Set Status")}>
            Update Status
          </Button>
          <Button onClick={() => setIsExtensionModalOpen(true)} variant="custom">
            Extend Date
          </Button>
        </div>
      )}

      {shouldShowAcknowledgeButton(ticketAssignees || [], user?.id) && (
        <div className="flex justify-end gap-2">
          <Button
            classNames="bg-sky-600 hover:bg-sky-700 text-white text-xs mt-4 w-full"
            onClick={async () => {
              const confirmApprovalPrompt = await confirmApproval("Confirmation", "Are you sure you want to acknowledge this rejected request?", undefined, "info", "Acknowledge");
              if (confirmApprovalPrompt.isConfirmed) {
                try {
                  // needed to check if closure status is needed
                  // postClosureStatusService( ticketId, { status: RequestStatus.Unresolved });
                  postUpdateTicketApproval({
                    ticketId,
                    payload: {
                      status: RequestStatus.Rejected,
                    }
                  });
                  putUpdateTicketStatus({ id: ticketId, status: RequestStatus.Rejected });

                  

                } catch (error) {
                  toast.error("Failed to acknowledge ticket");
                }
              }
            }}
          >
            Acknowledge
          </Button>
          <Button onClick={() => openModal("reconsider")} variant="custom">
            Reconsider
          </Button>
        </div>
      )}

      { status === RequestStatus.Approved && 
      !!findAssignExtendReceivingDepartment(ticketAssignees || [], user?.id) && 
      !hasAssignee &&(
        <div className="flex justify-end gap-2">
          <Button onClick={() => setAssignUserModalOpen(true)} classNames="bg-sky-600 hover:bg-sky-700 text-white text-xs mt-4 w-full">
            Assign Ticket
          </Button>
          <Button onClick={() => setIsExtensionModalOpen(true)} variant="custom">
            Extend Date
          </Button>
        </div>  
      )}
 
      {status === RequestStatus.Pending &&
        (!!findApproveRejectRequestManager(ticketAssignees || [], user?.id) || 
        !!findApproveRejectTopmanSignatory(ticketAssignees || [], user?.id) ||
        !!findApproveRejectReceivingDepartment(ticketAssignees || [], user?.id, createdBy || null)) && (
        <div className="flex justify-end gap-2">
          
          <Button
            classNames="bg-sky-600 hover:bg-sky-700 text-white text-xs mt-4 w-full"
            onClick={async () => {
              const result = await confirmApproval("Confirmation", message);
              if (result.isConfirmed) {
                try {
                 
                  if (!!findApproveRejectReceivingDepartment(ticketAssignees || [], user?.id, createdBy || null)) {
                    await putUpdateTicketStatus({ 
                      id: Number(ticketId), 
                      status: RequestStatus.Approved 
                    });
                    await postUpdateTicketApproval({
                      ticketId,
                      payload: {
                        status: RequestStatus.Approved,
                      }
                    });
                    // Refresh data after successful operations
                    // await handleFetchTicketByID();
                  }
                  
                 
                  else if (!!findApproveRejectRequestManager(ticketAssignees || [], user?.id)) {
                    showProcessingToast("Processing ticket approval...");
                    const approvalResponse = await updateTicketApprovalService(
                      ticketId,
                      {
                        status: RequestStatus.Approved,
                      }
                    );
                    // Close processing toast and show result
                    toast.dismiss();
                    // @ts-ignore
                    if (approvalResponse.status == "success") {
                      showSuccessToast("Ticket approval completed successfully!");
                      const approvalResult = await showApprovalSuccess();

                      if (approvalResult.isConfirmed) {
                        const vpResult = await confirmVPsApproval();
                        
                        if (vpResult.isConfirmed) {
                          setVpsApprovalModalOpen(true);
                          
                          // Let the modal handle the refresh after it closes
                          return; // Exit early to prevent the handleFetchTicketByID call below
                        }
                        // else {
                        //   await handleFetchTicketByID();
                        // }
                      }
                      // Refresh data after successful operations
                      await handleFetchTicketByID();
                    }
                  } else {
                     await postUpdateTicketApproval({
                    ticketId,
                    payload: {
                      ...topManPayload,
                    },
                  });
                  // Refresh data after successful operations
                  await handleFetchTicketByID();
                  }
                } catch (error: any) {}
              }
            }}
          >
            Approve
          </Button>
          {!doNotDisplayRejectButtonIfCreatorTicketIsVP(ticketAssignees || [], user?.id, createdBy || 0) && (
             <Button onClick={() => openModal("reject")} variant="custom">
            Reject
          </Button>
          )}
         
        </div>
      )}

      {!!findApproveRejectApprovalPresident(ticketAssignees || [], user?.id) && (
        <div className="flex justify-end gap-2">
           <Button classNames="bg-sky-600 hover:bg-sky-700 text-white text-xs mt-4 w-full"
            onClick={ async () => {
              const result = await confirmApproval("Confirmation", message);
              if (result.isConfirmed) {
                putUpdateTicketStatus({ 
                  id: Number(ticketId), 
                  status: RequestStatus.Approved 
                });
                postUpdateTicketApproval({
                  ticketId,
                  payload: {
                    status: RequestStatus.Approved,
                  }
                });
                // Refresh data after successful operations
                await handleFetchTicketByID();
              }
            }}>
            Approve
          </Button>
          <Button onClick={() => openModal("reject")} variant="custom">
            Reject
          </Button>
        </div>
      )}

      
        {/* For RECONSIDERED status */}
        
        {shouldShowForApprovalReceivingDepartmentButton(ticketAssignees || [], user?.id) && (
          <div className="flex justify-end gap-2">
            <Button
              classNames="bg-sky-600 hover:bg-sky-700 text-white text-xs mt-4 w-full"
              onClick={async () => {
                const result = await confirmApproval("Confirmation", message);
                if (result.isConfirmed) {
                  try {
                    await postUpdateTicketApproval({
                      ticketId,
                      payload: {
                        ...topManPayload,
                      },
                    });
                    // Refresh data after successful operations
                    await handleFetchTicketByID();
                  } catch (error: any) {}
                }
              }}
            >
              Approve
            </Button>
            <Button 
              onClick={() => {
              setApprovalContext('fromDepartment');
              setForApprovalModalOpen(true);
               }} variant="custom">
              For Approval
            </Button>
          </div>
        )}
        {shouldShowForApprovalVicePresidentButton(ticketAssignees || [], user?.id) && (
            <div className="flex justify-end gap-2">
              <Button
                classNames="bg-sky-600 hover:bg-sky-700 text-white text-xs mt-4 w-full"
                onClick={handleVicePresidentApproval}
              >
                Approve
              </Button>
              <Button 
                onClick={() => {
                setApprovalContext('fromVicePresident');
                setForApprovalModalOpen(true);
                }} variant="custom">
                For Approval
              </Button>
            </div>
        )} 
      <ForApprovalModal
        ticketId={ticketId}
        isOpen={forApprovalModalOpen}
        onClose={() => setForApprovalModalOpen(false)}
        onSubmit={handleApprovalSubmit}
        title="Ticket Approval Request"
        description={getApprovalDescription(approvalContext)}
        placeholder="Enter remarks"
        
      />
      <AssignUserModal isOpen={assignUserModalOpen} onClose={() => setAssignUserModalOpen(false)} ticketId={ticketId} handleFetchTicketByID={handleFetchTicketByID} />
      <RejectTicketModal ticketId={ticketId} isOpen={isRejectModalOpen} onClose={closeModal} onReject={handleReject} title={modalProps.title} placeholder={modalProps.placeholder} />
      <VPSApprovalModal 
        ticketId={ticketId} 
        isOpen={vpsApprovalModalOpen} 
        onClose={() => setVpsApprovalModalOpen(false)} 
        handleFetchTicketByID={handleFetchTicketByID} 
        shouldShowForApprovalReceivingDepartmentButton={shouldShowForApprovalReceivingDepartmentButton(ticketAssignees || [], user?.id)}
        remarks={remarks}/>
      <CloseTicketModal title={modalTitle} isOpen={isModalOpen} ticketId={ticketId.toString()} onClose={() => setIsModalOpen(false)} handleFetchTicketByID={handleFetchTicketByID} />
      <ExtensionDateModal isOpen={isExtensionModalOpen} onClose={() => setIsExtensionModalOpen(false)} handleFetchTicketByID={handleFetchTicketByID} expectedCompletionDate={expectedCompletionDate}/>
    </div>
  );
};

export default TicketDetails;
