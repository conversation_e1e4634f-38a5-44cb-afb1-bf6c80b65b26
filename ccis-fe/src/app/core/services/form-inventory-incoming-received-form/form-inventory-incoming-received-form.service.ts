import httpClient from "@clients/httpClient";
import { IDefaultParams } from "@interface/common.interface";
import { IIncomingReceivedFormPayload, IIncomingReceivedFormWithIndexPayload, TUpdateFormStatusPayload } from "@state/types/form-inventory-incoming-received-form";

const incomingReceivedFormApiResource = "form";

export const getIncomingReceivedFormService = async (id?: number) => {
  return httpClient.get(
    `${incomingReceivedFormApiResource}/${id}?relations=createdBy.position|division|formType|area|attachments|padAssignments.formTransmittal.approval.signatories.user.position|approval.signatories|padAssignments.formTransmittal.latestFormTransmittalTrail.releasedTo|padAssignments.padSeriesDetails`
  );
};

export const getIncomingReceivedFormsService = async (params: IDefaultParams) => {
  let query = `${incomingReceivedFormApiResource}?pageSize=${params.pageSize ?? 100}&page=${
    params.page ?? 1
  }&relations=createdBy.position|division|formType|area|attachments|padAssignments.formTransmittal.approval.signatories.user.position|approval.signatories|padAssignments.formTransmittal.latestFormTransmittalTrail.releasedTo&sort=id,desc`;

  if (params.page) {
    query += `&page=${params.page}`;
  }
  if (params.pageSize) {
    query += `&pageSize=${params.pageSize}`;
  }

  if (params.filter) {
    query += `&atpNumber[like]=${params.filter}`;
  }

  if (params.type) {
    query += `&formTypeId[eq]=${params.type}`;
  }
  if (params.statusFilter) {
    query += `&status[eq]=${params.statusFilter}`;
  }

  if (params.divisionFilter) {
    query += `&divisionId[eq]=${params.divisionFilter}`;
  }

  return httpClient.get(query);
};

export const getFormActivityLogsService = async (padAssignmentId: number) => {
  return httpClient.get(`pad-assignment/${padAssignmentId}/activity-logs?relations=causer.position`);
};

export const postIncomingReceivedFormService = async (payload: IIncomingReceivedFormPayload) => {
  const formData = new FormData();
  Object.keys(payload).forEach((key) => {
    if (key === "attachments" && Array.isArray(payload.attachments)) {
      payload.attachments.forEach((attachment, index) => {
        if (attachment.file) {
          formData.append(`attachments[${index}][file]`, attachment.file);
        }
        if (attachment.label !== undefined) {
          formData.append(`attachments[${index}][label]`, attachment.label);
        }
        if (attachment.description !== undefined) {
          formData.append(`attachments[${index}][description]`, attachment.description);
        }
      });
    } else {
      formData.append(key, (payload as any)[key]);
    }
  });
  return httpClient.post(incomingReceivedFormApiResource, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
};

export const putIncomingReceivedFormService = async (payload: IIncomingReceivedFormPayload) => {
  return httpClient.put(`${incomingReceivedFormApiResource}/${payload.id}`, payload);
};

export const destroyIncomingReceivedFormService = async (payload: IIncomingReceivedFormWithIndexPayload) => {
  return httpClient.delete(`${incomingReceivedFormApiResource}/${payload.id}`);
};
export const deleteAttachment = async (id: string) => {
  return httpClient.delete(`shared/attachments/${id}`);
};

//For verifaction List
export const getPendingFormsService = async (params: IDefaultParams) => {
  let query = `${incomingReceivedFormApiResource}?status[eq]=PENDING&relations=createdBy.position|division|formType|area|attachments|padAssignments.formTransmittal.approval.signatories.user.position|approval.signatories`;

  if (params.page) {
    query += `&page=${params.page}`;
  }
  if (params.pageSize) {
    query += `&pageSize=${params.pageSize}`;
  }

  if (params.divisionFilter) {
    query += `&divisionId[eq]=${params.divisionFilter}`;
  }

  return httpClient.get(query);
};

export const updateApprovalStatusService = async (approvalSignatoryId: string, payload: TUpdateFormStatusPayload) => {
  return httpClient.put(`/shared/approvals/${approvalSignatoryId}/status`, payload);
};

export const getAreaAdminsService = async () => {
  return httpClient.get(`/user-area/admins`);
};

export const getCompletedPadsService = async (params: IDefaultParams) => {
  let api = "pad-assignment/assigned-to-me";
  let relations =
    "relations=padSeriesDetails.cooperative|padSeriesDetails.product|formTransmittal|form.division|form.formType|form.area|formTransmittal|createdBy|padSeriesDetails.attachments&status[eq]=COMPLETED";

  let query = `${api}?pageSize=${params.pageSize ?? 10}&page=${params.page ?? 1}`;

  //Filter by division
  if (params?.divisionFilter) {
    query += `&divisionId[eq]=${params.divisionFilter}`;
  }

  //Filter by form type
  if (params?.formtypeFilter) {
    query += `&formTypeId[eq]=${params.formtypeFilter}`;
  }

  //Filter by area
  if (params?.areaFilter) {
    query += `&areaId[eq]=${params.areaFilter}`;
  }

  if (params?.dateFrom && params?.dateTo) {
    query += `&createdAt[between]=${params.dateFrom},${params.dateTo}`;
  }

  return httpClient.get(`${query}&${relations}&sort=id,desc`);
};
