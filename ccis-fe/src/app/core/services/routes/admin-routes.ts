import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
// import AdminDashboard from "@modules/admin/dashboard";
import AuthGuard from "@layouts/AuthGuard";
import TicketUtilities from "@modules/admin/departmental-ticketing-utilities";
import ApplicationUtility from "@modules/admin/departmental-ticketing-utilities/applications";
import DevicesSystems from "@modules/admin/departmental-ticketing-utilities/devices-systems";
import OperatingSystems from "@modules/admin/departmental-ticketing-utilities/operating-systems";
import RequestTypes from "@modules/admin/departmental-ticketing-utilities/request-types";
import FormInventoryUtilities from "@modules/admin/form-inventory-utilities";
import BankAccounts from "@modules/admin/form-inventory-utilities/bank-accounts";
import Banks from "@modules/admin/form-inventory-utilities/banks";
import Divisions from "@modules/admin/form-inventory-utilities/divisions";
import FormTypes from "@modules/admin/form-inventory-utilities/form-types";
import MarketAreas from "@modules/admin/form-inventory-utilities/market-areas";
import PaymentMethods from "@modules/admin/form-inventory-utilities/payment-methods";
import ReleasedMethod from "@modules/admin/form-inventory-utilities/released-methods";
import GlobalSettings from "@modules/admin/global-settings";
import FormInventorySettings from "@modules/admin/global-settings/form-inventory-settings";
import ProposalSettings from "@modules/admin/global-settings/product-proposal-settings";
import ProductProposal from "@modules/sales/product-proposal";
import Approval from "@modules/sales/product-proposal/Approval";
import ProductProposalForm from "@modules/sales/product-proposal/Components/Forms/ProductProposalForm";
import Revisions from "@modules/admin/product-revisions";
import ReviewRevision from "@modules/admin/product-revisions/components/Review/ReviewRevision";
import ProductUtilities from "@modules/admin/product-utilities";
import UtilitiesAreas from "@modules/admin/product-utilities/areas/index";
import UtilitiesCategory from "@modules/admin/product-utilities/category/index";
import UtilitiesCommissionType from "@modules/admin/product-utilities/commission-type";
import UtilitiesCommissionTypeAge from "@modules/admin/product-utilities/commission-type-age";
import ContestabilityTable from "@modules/admin/product-utilities/contestability";
import UtilitiesDepartments from "@modules/admin/product-utilities/departments";
import UtilitiesHeaders from "@modules/admin/product-utilities/headers/index";
import UtilitiesPositions from "@modules/admin/product-utilities/positions/index";
import UtilitiesTypes from "@modules/admin/product-utilities/product-types/index";
import UtilitiesSignatories from "@modules/admin/product-utilities/signatory-template";
import UtilitiesSignatoryType from "@modules/admin/product-utilities/signatory-type/index";
import UtilitiesTargetMarket from "@modules/admin/product-utilities/target-market/index";
import Products from "@modules/admin/products";
import CreateProductForm from "@modules/admin/products/components/Forms/CreateProductForm";
import EditProductDetailsForm from "@modules/admin/products/components/Forms/EditProductDetailsFom";
import EditProductForm from "@modules/admin/products/components/Forms/EditProductForm";
import EditProductRevision from "@modules/admin/products/components/Forms/EditProductRevision";
import Shares from "@modules/admin/shares";
import SharesCooperativesUtilities from "@modules/admin/shares-cooperatives-utilities";
import Affiliation from "@modules/admin/shares-cooperatives-utilities/affiliations";
import CooperativeCategory from "@modules/admin/shares-cooperatives-utilities/cooperative-category";
import CooperativeMembership from "@modules/admin/shares-cooperatives-utilities/cooperative-membership";
import CooperativeType from "@modules/admin/shares-cooperatives-utilities/cooperative-type";
import Requirements from "@modules/admin/shares-cooperatives-utilities/requirements";
import RequirementsTemplate from "@modules/admin/shares-cooperatives-utilities/requirements-template";
import Users from "@modules/admin/users-management";
import AdminDashboard from "@modules/dashboard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import Profile from "@modules/shared/profile";

//Insert import here:
import RemittancesUtilities from "@modules/admin/remittances-utilities";
import RemittanceData from "@modules/admin/remittances-utilities/remittance-data";
import RemittanceType from "@modules/admin/remittances-utilities/remittance-type";

import { FaBookmark, FaUserCog } from "react-icons/fa";
import { GiNotebook } from "react-icons/gi";
import { HiChevronRight } from "react-icons/hi";
import { LuFileCog, LuFileSpreadsheet } from "react-icons/lu";
import { MdDashboard } from "react-icons/md";
import { PiCertificate, PiList } from "react-icons/pi";
import { RiEqualizerLine } from "react-icons/ri";

import AerApprovalTable from "@modules/admin/approval-aer/table/AerApprovalTable";
import { FaFileCircleCheck } from "react-icons/fa6";
import QuotationFipAerView from "@modules/sales/view-aer/fipViewPreview";
import { QuotationClspQuotationView } from "@modules/sales/view-aer/clsp";
import { QuotationClppQuotationView } from "@modules/sales/view-aer/clpp";
import { QuotationGyrtQuotationView } from "@modules/sales/view-aer/gyrt";
import MyApprovals from "@modules/admin/approval-aer";
import NotificationPage from "@modules/shared/notification";
import CommissionAndRequirements from "@modules/marketing/validation/commission-and-requirements";
import ApprovalMarketing from "@modules/marketing/validation/commission-and-requirements/Approval";
import EmployeeReportForm from "@modules/shared/WeeklyAccomplishmentReport";
import { TbReportAnalytics } from "react-icons/tb";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.ADMIN.dashboard.key,
  path: ROUTES.ADMIN.dashboard.key,
  component: AdminDashboard,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: MdDashboard,
  isSidebar: true,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.ADMIN.notification.key,
  path: ROUTES.ADMIN.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.user],
  icon: MdDashboard,
  //   isSidebar: true,
};

export const users: RouteItem = {
  name: "User Management",
  id: ROUTES.ADMIN.users.key,
  path: ROUTES.ADMIN.users.key,
  component: Users,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: FaUserCog,
  isSidebar: true,
};

export const productUtilitiesTypes: RouteItem = {
  name: "Types",
  id: ROUTES.ADMIN.types.key,
  path: ROUTES.ADMIN.types.key,
  component: UtilitiesTypes,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
};

export const productUtilitiesCategory: RouteItem = {
  name: "Category",
  id: ROUTES.ADMIN.category.key,
  path: ROUTES.ADMIN.category.key,
  component: UtilitiesCategory,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
};

export const productUtilitiesCommissionType: RouteItem = {
  name: "Commission Type",
  id: ROUTES.ADMIN.commissionType.key,
  path: ROUTES.ADMIN.commissionType.key,
  component: UtilitiesCommissionType,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
};
export const productUtilitiesCommissionTypeAge: RouteItem = {
  name: "Commission Type Age",
  id: ROUTES.ADMIN.commissionTypeAge.key,
  path: ROUTES.ADMIN.commissionTypeAge.key,
  component: UtilitiesCommissionTypeAge,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
};

export const productUtilitiesSignatories: RouteItem = {
  name: "Signatory Template",
  id: ROUTES.ADMIN.signatories.key,
  path: ROUTES.ADMIN.signatories.key,
  component: UtilitiesSignatories,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
};

export const productUtilitiesSignatoryType: RouteItem = {
  name: "Signatory Type",
  id: ROUTES.ADMIN.signatoryType.key,
  path: ROUTES.ADMIN.signatoryType.key,
  component: UtilitiesSignatoryType,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
};

export const productUtilitiesDeparments: RouteItem = {
  name: "Departments",
  id: ROUTES.ADMIN.departments.key,
  path: ROUTES.ADMIN.departments.key,
  component: UtilitiesDepartments,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
};

export const productUtilitiesPositions: RouteItem = {
  name: "Positions",
  id: ROUTES.ADMIN.positions.key,
  path: ROUTES.ADMIN.positions.key,
  component: UtilitiesPositions,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
};
export const productUtilitiesTargetMarket: RouteItem = {
  name: "Target Market",
  id: ROUTES.ADMIN.targetMarket.key,
  path: ROUTES.ADMIN.targetMarket.key,
  component: UtilitiesTargetMarket,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
};

export const productUtilitiesHeaders: RouteItem = {
  name: "Headers",
  id: ROUTES.ADMIN.headers.key,
  path: ROUTES.ADMIN.headers.key,
  component: UtilitiesHeaders,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
};

export const productUserAreas: RouteItem = {
  name: "Areas",
  id: ROUTES.ADMIN.userAreas.key,
  path: ROUTES.ADMIN.userAreas.key,
  component: UtilitiesAreas,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
};

export const productUtilitiesContestability: RouteItem = {
  name: "Contestability",
  id: ROUTES.ADMIN.contestability.key,
  path: ROUTES.ADMIN.contestability.key,
  component: ContestabilityTable,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
};

export const productUtilities: RouteItem = {
  name: "Product Utilities",
  id: ROUTES.ADMIN.productUtilities.key,
  path: ROUTES.ADMIN.productUtilities.key,
  component: ProductUtilities,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: LuFileCog,
  isSidebar: true,
  children: [
    productUtilitiesHeaders,
    productUtilitiesTypes,
    productUtilitiesCategory,

    productUtilitiesCommissionType,
    productUtilitiesCommissionTypeAge,
    productUtilitiesSignatories,
    productUtilitiesSignatoryType,
    productUtilitiesDeparments,
    productUtilitiesPositions,
    productUtilitiesTargetMarket,
    productUserAreas,
    productUtilitiesContestability,
  ],
};

export const profile: RouteItem = {
  name: "Admin Profile",
  id: ROUTES.ADMIN.profile.key,
  path: ROUTES.ADMIN.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.admin],
};

export const createGuidelines: RouteItem = {
  name: "Create Product Guidelines",
  id: ROUTES.ADMIN.createGuidelines.key,
  path: ROUTES.ADMIN.createGuidelines.key,
  component: CreateProductForm,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  isSidebar: false,
};

export const editGuidelines: RouteItem = {
  name: "Edit Product Guidelines",
  id: ROUTES.ADMIN.editGuidelines.key,
  path: ROUTES.ADMIN.editGuidelines.key,
  component: EditProductForm,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  isSidebar: false,
};

export const editProductDetails: RouteItem = {
  name: "Edit Product Details",
  id: ROUTES.ADMIN.editProductDetails.key,
  path: ROUTES.ADMIN.editProductDetails.key,
  component: EditProductDetailsForm,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  isSidebar: false,
};

export const products: RouteItem = {
  name: "Products",
  id: ROUTES.ADMIN.products.key,
  path: ROUTES.ADMIN.products.key,
  component: Products,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: LuFileSpreadsheet,
  isSidebar: true,
};

export const revisions: RouteItem = {
  name: "Product Revisions",
  id: ROUTES.ADMIN.revisions.key,
  path: ROUTES.ADMIN.revisions.key,
  component: Revisions,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: LuFileSpreadsheet,
};

export const shares: RouteItem = {
  name: "Shares",
  id: ROUTES.ADMIN.shares.key,
  path: ROUTES.ADMIN.shares.key,
  component: Shares,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: PiCertificate,
  isSidebar: true,
};

export const sharescooperativeUtilitiesAffiliationTable: RouteItem = {
  name: "Affiliation",
  id: ROUTES.ADMIN.affiliation.key,
  path: ROUTES.ADMIN.affiliation.key,
  component: Affiliation,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
};
export const sharescooperativeUtilitiesCategory: RouteItem = {
  name: "Coop Category",
  id: ROUTES.ADMIN.cooperativeCategoryTable.key,
  path: ROUTES.ADMIN.cooperativeCategoryTable.key,
  component: CooperativeCategory,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
};
export const sharescooperativeUtilitiesTypes: RouteItem = {
  name: "Coop Type",
  id: ROUTES.ADMIN.cooperativeType.key,
  path: ROUTES.ADMIN.cooperativeType.key,
  component: CooperativeType,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
};
export const sharescooperativeUtilitiesRequirements: RouteItem = {
  name: "Requirements",
  id: ROUTES.ADMIN.requirements.key,
  path: ROUTES.ADMIN.requirements.key,
  component: Requirements,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
};

export const sharescooperativeUtilitiesRequirementsTemplate: RouteItem = {
  name: "Requirements Template",
  id: ROUTES.ADMIN.requirementsTemplate.key,
  path: ROUTES.ADMIN.requirementsTemplate.key,
  component: RequirementsTemplate,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
};
export const sharescooperativeUtilitiesMembership: RouteItem = {
  name: "Coop Membership Type",
  id: ROUTES.ADMIN.cooperetiveMembership.key,
  path: ROUTES.ADMIN.cooperetiveMembership.key,
  component: CooperativeMembership,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
};
export const sharescooperativesUtilities: RouteItem = {
  name: "Shares & Coop Utilities",
  id: ROUTES.ADMIN.sharescooperativesUtilities.key,
  path: ROUTES.ADMIN.sharescooperativesUtilities.key,
  component: SharesCooperativesUtilities,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: LuFileCog,
  isSidebar: true,
  children: [
    sharescooperativeUtilitiesAffiliationTable,
    sharescooperativeUtilitiesCategory,
    sharescooperativeUtilitiesMembership,
    sharescooperativeUtilitiesTypes,
    sharescooperativeUtilitiesRequirements,
    sharescooperativeUtilitiesRequirementsTemplate,
  ],
};
export const editProductRevision: RouteItem = {
  name: "Edit Product Revision",
  id: ROUTES.ADMIN.editRevision.key,
  path: ROUTES.ADMIN.editRevision.key,
  component: EditProductRevision,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  isSidebar: false,
};

export const cloneProductGuideline: RouteItem = {
  name: "Clone Product Guidelines",
  id: ROUTES.ADMIN.cloneProductGuideline.key,
  path: ROUTES.ADMIN.cloneProductGuideline.key,
  component: EditProductRevision,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  isSidebar: false,
};

export const cloneProduct: RouteItem = {
  name: "Clone Product",
  id: ROUTES.ADMIN.cloneProduct.key,
  path: ROUTES.ADMIN.cloneProduct.key,
  component: CreateProductForm,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  isSidebar: false,
};

export const reviewRevisions: RouteItem = {
  name: "Review Product Revisions",
  id: ROUTES.ADMIN.reviewRevisions.key,
  path: ROUTES.ADMIN.reviewRevisions.key,
  component: ReviewRevision,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: LuFileSpreadsheet,
};

export const proposal: RouteItem = {
  name: "Proposal",
  id: ROUTES.ADMIN.productProposal.key,
  path: ROUTES.ADMIN.productProposal.key,
  component: ProductProposal,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: GiNotebook,
  isSidebar: true,
};

export const viewProposal: RouteItem = {
  name: "View Proposal",
  id: ROUTES.ADMIN.viewProductProposal.key,
  path: ROUTES.ADMIN.viewProductProposal.key,
  component: Approval,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: GiNotebook,
  isSidebar: false,
};

export const createProductProposal: RouteItem = {
  name: "Create Product Proposal",
  id: ROUTES.ADMIN.createProductProposal.key,
  path: ROUTES.ADMIN.createProductProposal.key,
  component: ProductProposalForm,
  guard: AuthGuard,
  roles: [UserRoles.admin],
};

export const editProductProposal: RouteItem = {
  name: "Edit Product Proposal",
  id: ROUTES.ADMIN.editProductProposal.key,
  path: ROUTES.ADMIN.editProductProposal.key,
  component: ProductProposalForm,
  guard: AuthGuard,
  roles: [UserRoles.admin],
};

export const globalSettings: RouteItem = {
  name: "Settings",
  id: ROUTES.ADMIN.globalSettings.key,
  path: ROUTES.ADMIN.globalSettings.key,
  component: GlobalSettings,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: RiEqualizerLine,
  isSidebar: true,
};
export const proposalSettings: RouteItem = {
  name: "Proposal Settings",
  id: ROUTES.ADMIN.proposalSettings.key,
  path: ROUTES.ADMIN.proposalSettings.key,
  component: ProposalSettings,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: RiEqualizerLine,
};

export const formInventoryUtilitiesDivision: RouteItem = {
  name: "Division",
  id: ROUTES.ADMIN.divisions.key,
  path: ROUTES.ADMIN.divisions.key,
  component: Divisions,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
};
export const formInventoryUtilitiesFormType: RouteItem = {
  name: "Form Types",
  id: ROUTES.ADMIN.formTypes.key,
  path: ROUTES.ADMIN.formTypes.key,
  component: FormTypes,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
};

export const formInventoryUtilitiesPaymentMethods: RouteItem = {
  name: "Payment Methods",
  id: ROUTES.ADMIN.paymentMethods.key,
  path: ROUTES.ADMIN.paymentMethods.key,
  component: PaymentMethods,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
};
export const formInventoryUtilitiesBanks: RouteItem = {
  name: "Banks",
  id: ROUTES.ADMIN.banks.key,
  path: ROUTES.ADMIN.banks.key,
  component: Banks,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
};
export const formInventoryUtilitiesMarketAreas: RouteItem = {
  name: "Market Areas",
  id: ROUTES.ADMIN.marketAreas.key,
  path: ROUTES.ADMIN.marketAreas.key,
  component: MarketAreas,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
};

export const formInventoryUtilitiesBankAccounts: RouteItem = {
  name: "Bank Accounts",
  id: ROUTES.ADMIN.bankAccounts.key,
  path: ROUTES.ADMIN.bankAccounts.key,
  component: BankAccounts,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
};
export const formInventoryUtilitiesReleasedMethods: RouteItem = {
  name: "Released Methods",
  id: ROUTES.ADMIN.releasedMethods.key,
  path: ROUTES.ADMIN.releasedMethods.key,
  component: ReleasedMethod,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
};
export const formInventoryUtilities: RouteItem = {
  name: "Form Inventory Utilities",
  id: ROUTES.ADMIN.formInventoryUtilities.key,
  path: ROUTES.ADMIN.formInventoryUtilities.key,
  component: FormInventoryUtilities,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: LuFileSpreadsheet,
  isSidebar: true,
  children: [
    formInventoryUtilitiesDivision,
    formInventoryUtilitiesFormType,
    formInventoryUtilitiesPaymentMethods,
    formInventoryUtilitiesBanks,
    formInventoryUtilitiesMarketAreas,
    formInventoryUtilitiesBankAccounts,
    formInventoryUtilitiesReleasedMethods,
  ],
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.ADMIN.requestDashboard.key,
  path: ROUTES.ADMIN.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.ADMIN.requestForm.key,
  path: ROUTES.ADMIN.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.ADMIN.viewRequestForm.key,
  path: ROUTES.ADMIN.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  isSidebar: false,
};
export const requestTypeUtilities: RouteItem = {
  name: "Request Type",
  id: ROUTES.ADMIN.requestTypesUtility.key,
  path: ROUTES.ADMIN.requestTypesUtility.key,
  component: RequestTypes,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
  isSidebar: false,
};

export const applicationUtilities: RouteItem = {
  name: "Applications",
  id: ROUTES.ADMIN.applicationsUtility.key,
  path: ROUTES.ADMIN.applicationsUtility.key,
  component: ApplicationUtility,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
  isSidebar: false,
};

export const operatingSystemsUtilities: RouteItem = {
  name: "Operating Systems",
  id: ROUTES.ADMIN.operatingSystemsUtility.key,
  path: ROUTES.ADMIN.operatingSystemsUtility.key,
  component: OperatingSystems,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
  isSidebar: false,
};

export const devicesUtilities: RouteItem = {
  name: "Devices/Systems",
  id: ROUTES.ADMIN.devicesSystemUtility.key,
  path: ROUTES.ADMIN.devicesSystemUtility.key,
  component: DevicesSystems,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
  isSidebar: false,
};

export const ticketUtilities: RouteItem = {
  name: "Ticket Utilities",
  id: ROUTES.ADMIN.ticketUtilities.key,
  path: ROUTES.ADMIN.ticketUtilities.key,
  component: TicketUtilities,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: FaBookmark,
  isSidebar: true,
  children: [requestTypeUtilities, applicationUtilities, devicesUtilities, operatingSystemsUtilities],
};
export const formInventorySettings: RouteItem = {
  name: "Form Inventory Settings",
  id: ROUTES.ADMIN.formInventorylSettings.key,
  path: ROUTES.ADMIN.formInventorylSettings.key,
  component: FormInventorySettings,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: RiEqualizerLine,
};

export const remittanceUtilitiesType: RouteItem = {
  name: "Remittance Type",
  id: ROUTES.ADMIN.remittanceType.key,
  path: ROUTES.ADMIN.remittanceType.key,
  component: RemittanceType,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
};

export const remittanceUtilitiesData: RouteItem = {
  name: "Remittance Data",
  id: ROUTES.ADMIN.remittanceData.key,
  path: ROUTES.ADMIN.remittanceData.key,
  component: RemittanceData,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
};

export const remittancesUtilities: RouteItem = {
  name: "Remittances Utilities",
  id: ROUTES.ADMIN.remittancesUtilities.key,
  path: ROUTES.ADMIN.remittancesUtilities.key,
  component: RemittancesUtilities,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: RiEqualizerLine,
  isSidebar: true,
  children: [remittanceUtilitiesType, remittanceUtilitiesData],
};

export const aerTableApproval: RouteItem = {
  name: "AER Approvals",
  id: ROUTES.ADMIN.aerApproval.key,
  path: ROUTES.ADMIN.aerApproval.key,
  component: AerApprovalTable,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
  isSidebar: false,
};
export const quotationFipAerView: RouteItem = {
  name: "Quotation FIP Aer View",
  id: ROUTES.ADMIN.quotationFipAerView.key,
  path: ROUTES.ADMIN.quotationFipAerView.key,
  component: QuotationFipAerView,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: PiList,
  isSidebar: false,
};
export const quotationClspQuotationView: RouteItem = {
  name: "Quotation CLSP Quotation View",
  id: ROUTES.ADMIN.quotationClspQuotationView.key,
  path: ROUTES.ADMIN.quotationClspQuotationView.key,
  component: QuotationClspQuotationView,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: PiList,
  isSidebar: false,
};
export const quotationClppQuotationView: RouteItem = {
  name: "Quotation CLPP Quotation View",
  id: ROUTES.ADMIN.quotationClppQuotationView.key,
  path: ROUTES.ADMIN.quotationClppQuotationView.key,
  component: QuotationClppQuotationView,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: PiList,
  isSidebar: false,
};
export const quotationGyrtQuotationView: RouteItem = {
  name: "Quotation GYRT Quotation View",
  id: ROUTES.ADMIN.quotationGyrtQuotationView.key,
  path: ROUTES.ADMIN.quotationGyrtQuotationView.key,
  component: QuotationGyrtQuotationView,
  guard: AuthGuard,
  roles: [UserRoles.sales],
  icon: PiList,
  isSidebar: false,
};
export const commissionAndRequirements: RouteItem = {
  name: "Commission and Requirements",
  id: ROUTES.ADMIN.commissionAndRequirements.key,
  path: ROUTES.ADMIN.commissionAndRequirements.key,
  component: CommissionAndRequirements,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: HiChevronRight,
};
export const myApprovals: RouteItem = {
  name: "My Approvals",
  id: ROUTES.ADMIN.myApprovals.key,
  path: ROUTES.ADMIN.myApprovals.key,
  component: MyApprovals,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: FaFileCircleCheck,
  isSidebar: true,
  children: [aerTableApproval, commissionAndRequirements],
};
export const approval: RouteItem = {
  name: "Approval",
  id: ROUTES.ADMIN.viewProductProposalSignatory.key,
  path: ROUTES.ADMIN.viewProductProposalSignatory.key,
  component: ApprovalMarketing,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  isSidebar: false,
};
export const weeklyAccomplishmentReport: RouteItem = {
  name: "Weekly Accomplishment Report",
  id: ROUTES.ADMIN.weeklyAccomplishmentReport.key,
  path: ROUTES.ADMIN.weeklyAccomplishmentReport.key,
  component: EmployeeReportForm,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  icon: TbReportAnalytics,
  isSidebar: true,
}
export const adminRoutes = [
  overview,
  users,
  products,
  proposal,
  editProductDetails,
  editGuidelines,
  createGuidelines,
  productUtilities,
  profile,
  revisions,
  shares,
  sharescooperativesUtilities,
  editProductRevision,
  reviewRevisions,
  viewProposal,
  cloneProductGuideline,
  cloneProduct,
  createProductProposal,
  editProductProposal,
  globalSettings,
  proposalSettings,
  formInventoryUtilities,
  requestDashboard,
  requestForm,
  viewRequest,
  ticketUtilities,
  formInventorySettings,
  remittancesUtilities,
  remittanceUtilitiesData,
  remittanceUtilitiesType,
  // aerTableApproval,
  quotationFipAerView,
  quotationClspQuotationView,
  quotationClppQuotationView,
  quotationGyrtQuotationView,
  myApprovals,
  notification,
  commissionAndRequirements,
  approval,
  weeklyAccomplishmentReport
];
