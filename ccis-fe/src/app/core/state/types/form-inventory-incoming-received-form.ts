import { PayloadAction } from "@reduxjs/toolkit";
import { IAttachments, LoadingResult } from "@interface/common.interface";
import { IFormsResponse, IIncomingReceivedForm, IPadAssignments } from "@interface/form-inventory.interface";
import { IDivision, IFormType } from "@interface/form-inventory-utilities";
import { IUserArea } from "@interface/utilities.interface";

// State type for managing incomingReceivedForms
export type TIncomingReceivedFormState = {
  incomingReceivedForms: IIncomingReceivedForm[];
  selectedIncomingReceivedForm: TIncomingReceivedFormPayloadWithIndex;
  getIncomingReceivedForms: GetForms;
  getIncomingReceivedForm: LoadingResult;
  getPendingForms: LoadingResult;
  postIncomingReceivedForm?: TIncomingReceivedFormResponse;
  putIncomingReceivedForm?: TIncomingReceivedFormResponse;
  destroyIncomingReceivedForm?: LoadingResult;
  incomingReceivedFormAttachment?: IAttachments[];
  getCompletedPads: LoadingResult;
  selectedCompletedPads: IPadAssignments[];
};

// Extending LoadingResult type to include a single IMasterlist data
export type TIncomingReceivedFormResponse = LoadingResult & {
  data?: IIncomingReceivedForm; // Optional masterlist data
};

// Define the structure for IncomingReceivedForm data
export type TIncomingReceivedFormPayload = {
  id: number;
  divisionId: number;
  formTypeId: number;
  areaId: number;
  receivedDate: string;
  seriesFrom: number;
  seriesTo: number;
  atpNumber: string;
  padAssignments?: IPadAssignments[];
  noPads: number;
  attachments: IAttachments[];
};

// Payload type for creating or updating a incomingReceivedForm
export type IIncomingReceivedFormPayload = {
  id: number;
  divisionId: number;
  formTypeId: number;
  areaId: number;
  receivedDate: string;
  seriesFrom: number;
  seriesTo: number;
  atpNumber: string;
  padAssignments?: IPadAssignments[];
  noPads: number;
  attachments: IAttachments[];
  division?: IDivision;
  area?: IUserArea;
  formType?: IFormType;
};

export type TUpdateFormStatusPayload = {
  status: string;
  remarks?: string;
};

// Payload type for deleting a incomingReceivedForm
export type IIncomingReceivedFormWithIndexPayload = {
  id: number;
  index?: number;
};

export type TGetIncomingReceivedFormsWithFilterActionPayload = PayloadAction<{
  filter: string;
  id?: number | string;
}>;

// Defining payload that handles TMasterlistPayload type as data
export type TIncomingReceivedFormPayloadWithIndex = {
  data: IIncomingReceivedFormPayload;
  index: number;
};

export type TIIncomingReceivedFormWithIndexPayload = {
  filter: string;
  id: number;
};
export type GetForms = LoadingResult & {
  data?: IFormsResponse;
};

// Action payloads for incomingReceivedForms
export type TIncomingReceivedFormPayloadAction = PayloadAction<TIncomingReceivedFormPayload>;
// export type TIncomingReceivedFormActionPayload = PayloadAction<{ data?: IIncomingReceivedForm[] }>;
export type TIIncomingReceivedFormActionPayload = PayloadAction<IIncomingReceivedForm>;
export type TIncomingReceivedFormActionPayloadIIncomingReceivedForm = PayloadAction<TIncomingReceivedFormPayload>;

export type TIncomingReceivedFormWithIndexActionPayload = PayloadAction<TIncomingReceivedFormPayloadWithIndex>;
export type TIncomingReceivedFormIDPayloadActionPayload = PayloadAction<IIncomingReceivedFormWithIndexPayload>;
export type TGetIncomingReceivedFormWithFilterActionPayload = PayloadAction<{ filter?: string; id?: number }>;

// Payload for operations involving index and incomingReceivedForm data
// export type TIncomingReceivedFormWithIndexActionPayload = PayloadAction<{ data: TIncomingReceivedFormPayload, index: number }>;
