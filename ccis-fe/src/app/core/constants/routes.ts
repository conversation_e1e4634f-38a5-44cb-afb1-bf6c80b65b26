import { UserRoles } from "@interface/routes.interface";
export const adminBasePath = "/admin";
export const authBasePath = "/auth";
export const userBasePath = "/user";
export const uatUserBasePath = "/uat";
export const uatAdminBasePath = "/uat-admin";
export const marketingBasePath = "/marketing";
export const actuaryBasePath = "/actuary";
export const cashierBasePath = "/cashier";
export const salesBasePath = "/sales";
export const treasuryBasePath = "/treasury";
export const salesExecutiveAssistantBasePath = "/sales-executive-assistant";
export const complianceBasePath = "/compliance";
export const incomingCashierBasePath = "/incoming-cashier";
export const outgoingCashierBasePath = "/outgoing-cashier";
export const incomingAdminBasePath = "/incoming-admin";
export const outgoingAdminBasePath = "/outgoing-admin";
export const chiefCashierBasePath = "/chief-cashier";
export const clifsaAdminBasePath = "/clifsa-admin";
export const gamBasePath = "/gam";
export const incomingOutgoingCashierBasePath = "/incoming-outgoing-cashier";
export const underwritingBasePath = "/underwriting";
export const claimsBasePath = "/claims";
export const actuaryAnalyst1BasePath = "/" + UserRoles.actuaryAnalyst1;
export const actuaryAssistant1BasePath = "/" + UserRoles.actuaryAssistant1;
export const actuaryManagerBasePath = "/" + UserRoles.actuaryManager;
export const avpLifeNonLifeManagerBasePath = "/" + UserRoles.avpLifeNonLife;
export const researchAndDevelopmentBasePath = "/" + UserRoles.rnd;
export const claimsManagerBasePath = "/" + UserRoles.claimsManager;
export const vicePresidentSalesBasePath = "/" + UserRoles.vicePresidentSales;
export const vicePresidentOperationsBasePath = "/" + UserRoles.vicePresidentOperations;
export const presidentCeoBasePath = "/" + UserRoles.presidentCeo;
export const areaSalesManagerBasePath = "/" + UserRoles.areaSalesManager;
export const infraOfficerBasePath = "/" + UserRoles.infraOfficer;
export const accountingBasePath = "/" + UserRoles.accounting;
export const ROUTES = {
  AUTH: {
    login: {
      key: authBasePath + "/",
    },
  },

  ADMIN: {
    notification: { key: adminBasePath + "/notification" },
    profile: {
      key: adminBasePath + "/profile",
    },
    dashboard: {
      key: adminBasePath + "/dashboard",
    },
    users: {
      key: adminBasePath + "/users-management",
    },
    roles: {
      key: adminBasePath + "/roles-management",
    },
    role: {
      key: adminBasePath + "/role-management",
    },
    productUtilities: {
      key: adminBasePath + "/product-utilities",
    },
    types: {
      key: adminBasePath + "/product-utilities/types",
    },
    category: {
      key: adminBasePath + "/product-utilities/category",
    },
    commissionType: {
      key: adminBasePath + "/product-utilities/commissionType",
    },
    commissionTypeAge: {
      key: adminBasePath + "/product-utilities/commissionTypeAge",
    },
    signatories: {
      key: adminBasePath + "/product-utilities/signatories",
    },
    signatoryType: {
      key: adminBasePath + "/product-utilities/signatoryType",
    },
    departments: {
      key: adminBasePath + "/product-utilities/departments",
    },
    positions: {
      key: adminBasePath + "/product-utilities/positions",
    },
    targetMarket: {
      key: adminBasePath + "/product-utilities/targetMarket",
    },
    headers: {
      key: adminBasePath + "/product-utilities/headers",
    },
    contestability: {
      key: adminBasePath + "/product-utilities/contestability",
    },
    userAreas: {
      key: adminBasePath + "/product-utilities/user-area",
    },
    products: {
      key: adminBasePath + "/products",
    },
    revisions: {
      key: adminBasePath + "/products/:productid/revisions",
      parse: (pid: string) => adminBasePath + `/products/${pid}/revisions`,
    },
    reviewRevisions: {
      key: adminBasePath + "/products/:productid/revision/:revisionid",
      parse: (pid: string, rid: string) => adminBasePath + `/products/${pid}/revision/${rid}`,
    },
    editProductDetails: {
      key: adminBasePath + "/product/details/:id",
      parse: (id: string) => adminBasePath + `/product/details/${id}`,
    },
    guidelines: {
      key: adminBasePath + "/products/guidelines",
    },
    createGuidelines: {
      key: adminBasePath + "/products/guidelines/create",
    },
    editGuidelines: {
      key: adminBasePath + "/products/guidelines/edit/:id",
      parse: (id: string) => adminBasePath + `/products/guidelines/edit/${id}`,
    },
    editRevision: {
      key: adminBasePath + "/products/revisions/edit/:id",
      parse: (id: string) => adminBasePath + `/products/revisions/edit/${id}`,
    },
    cloneProductGuideline: {
      key: adminBasePath + "/products/guidelines/clone",
    },
    cloneProduct: {
      key: adminBasePath + "/products/clone",
    },
    reviewProductRevisionsFromEmail: {
      key: adminBasePath + "/products/:productid/revision/:revisionid/review",
      parse: (pid: string, rid: string) => adminBasePath + `/products/${pid}/revision/${rid}/review`,
    },
    remittancesUtilities: {
      key: adminBasePath + "/remittances-utilities",
    },
    remittanceType: {
      key: adminBasePath + "/remittances-utilities/remittance-type",
    },
    remittanceData: {
      key: adminBasePath + "/remittances-utilities/remittance-data",
    },
    shares: {
      key: adminBasePath + "/shares",
    },
    sharescooperativesUtilities: {
      key: adminBasePath + "/shares-cooperatives-utilities",
    },
    affiliation: {
      key: adminBasePath + "/shares-cooperatives-utilities/affiliations",
    },
    cooperativeCategoryTable: {
      key: adminBasePath + "/shares-cooperatives-utilities/cooperative-category",
    },
    cooperativeType: {
      key: adminBasePath + "/shares-cooperatives-utilities/cooperative-type",
    },
    requirements: {
      key: adminBasePath + "/shares-cooperatives-utilities/requirements",
    },
    requirementsTemplate: {
      key: adminBasePath + "/shares-cooperatives-utilities/requirement-template",
    },
    cooperetiveMembership: {
      key: adminBasePath + "/shares-cooperatives-utilities/cooperative-membership",
    },
    productProposal: {
      key: adminBasePath + "/product-proposal",
    },
    viewProductProposalCustomize: {
      key: adminBasePath + "/product-proposal/:id",
      parse: (id: string) => adminBasePath + `/product-proposal/${id}`,
    },
    viewProductProposal: {
      key: adminBasePath + "/product-proposal/:id",
      parse: (id: string) => adminBasePath + `/product-proposal/${id}`,
    },
    createProductProposal: {
      key: adminBasePath + "/product-proposal/create",
    },
    editProductProposal: {
      key: adminBasePath + "/product-proposal/edit/:id",
      parse: (id: string) => adminBasePath + `/product-proposal/edit/${id}`,
    },
    // compliance: {
    //   key: adminBasePath + "/compliance",
    // },
    // viewCompliance: {
    //   key: adminBasePath + "/compliance/:id",
    //   parse: (id: string) => adminBasePath + `/compliance/${id}`,
    // },
    formInventoryUtilities: {
      key: adminBasePath + "/form-inventory-utilities",
    },
    formInventoryUtilitiesDivision: {
      key: adminBasePath + "/form-inventory-utilities/form-inventory-division",
    },
    formInventoryUtilitiesType: {
      key: adminBasePath + "/form-inventory-utilities/form-inventory-type",
    },
    globalSettings: {
      key: adminBasePath + "/global-settings",
    },
    proposalSettings: {
      key: adminBasePath + "/global-settings/proposal-settings",
    },
    divisions: {
      key: adminBasePath + "/form-inventory-utilities/divisions",
    },
    formTypes: {
      key: adminBasePath + "/form-inventory-utilities/form-types",
    },
    paymentMethods: {
      key: adminBasePath + "/form-inventory-utilities/payment-methods",
    },
    banks: {
      key: adminBasePath + "/form-inventory-utilities/banks",
    },
    incomingOutgoingCashierDashboard: {
      key: adminBasePath + "/incoming-outgoing-cashier-dashboard",
    },
    marketAreas: {
      key: adminBasePath + "/form-inventory-utilities/market-areas",
    },
    bankAccounts: {
      key: adminBasePath + "/form-inventory-utilities/bank-accounts",
    },
    releasedMethods: {
      key: adminBasePath + "/form-inventory-utilities/released-methods",
    },
    requestDashboard: {
      key: adminBasePath + "/request-dashboard",
    },
    requestForm: {
      key: adminBasePath + "/request-form",
    },
    viewRequestForm: {
      key: adminBasePath + "/request-form/:id",
      parse: (id: string) => adminBasePath + `/request-form/${id}`,
    },
    ticketUtilities: {
      key: adminBasePath + "/ticket-utilities",
    },
    requestTypesUtility: {
      key: adminBasePath + "/ticket-utilities/request-type",
    },
    operatingSystemsUtility: {
      key: adminBasePath + "/ticket-utilities/operating-system",
    },
    applicationsUtility: {
      key: adminBasePath + "/ticket-utilities/applications",
    },
    devicesSystemUtility: {
      key: adminBasePath + "/ticket-utilities/devices-system",
    },
    formInventorylSettings: {
      key: adminBasePath + "/global-settings/form-inventory-settings",
    },
    quotationFipAerView: {
      key: adminBasePath + "/quotations/aer-view/fip/:id",
      parse: (id: string) => adminBasePath + "/quotations/aer-view/fip/" + `${id}`,
    },
    myApprovals: {
      key: adminBasePath + "/my-approvals",
    },
    aerApproval: {
      key: adminBasePath + "/my-approvals/aer-approvals",
    },

    quotationClspQuotationView: {
      key: adminBasePath + "/quotations/quotation-view/clsp/:id",
      parse: (id: string) => adminBasePath + "/quotations/quotation-view/clsp/" + `${id}`,
    },
    quotationClppQuotationView: {
      key: adminBasePath + "/quotations/quotation-view/clpp/:id",
      parse: (id: string) => adminBasePath + "/quotations/quotation-view/clpp/" + `${id}`,
    },
    quotationGyrtQuotationView: {
      key: adminBasePath + "/quotations/quotation-view/gyrt/:id",
      parse: (id: string) => adminBasePath + "/quotations/quotation-view/gyrt/" + `${id}`,
    },
    commissionAndRequirements: {
      key: adminBasePath + "/my-approvals/validation/commission-and-requirements",
    },
    viewProductProposalSignatory: {
      key: adminBasePath + "/approvals/validation/commission-and-requirements/view/:id",
      parse: (id: string) => adminBasePath + `/approvals/validation/commission-and-requirements/view/${id}`,
    },
    weeklyAccomplishmentReport: {
      key: adminBasePath + "/weekly-accomplishment-report",
    }
  },

  USERS: {
    notification: { key: userBasePath + "/notification" },
    dashboard: {
      key: userBasePath + "/dashboard",
    },
    approvals: {
      key: userBasePath + "/approvals",
    },
    reviewProductRevisions: {
      key: userBasePath + "/approvals/review/product/:productid/revision/:revisionid",
      parse: (pid: string, rid: string) => userBasePath + `/approvals/review/product/${pid}/revision/${rid}`,
    },
    profile: {
      key: userBasePath + "/profile",
    },
    reviewProductRevisionsFromEmail: {
      key: "/products/:productid/revision/:revisionid/review",
      parse: (pid: string, rid: string) => `/products/${pid}/revision/${rid}/review`,
    },
  },
  UATUSERS: {
    notification: { key: uatUserBasePath + "/notification" },
    uatUser: {
      key: uatUserBasePath + "/uat-sheet",
    },
    profile: {
      key: uatUserBasePath + "/profile",
    },
  },
  UATADMIN: {
    uatManagement: {
      key: uatAdminBasePath + "/uat-management",
    },
    uatResults: {
      key: uatAdminBasePath + "/uat-results",
    },
    notification: { key: uatAdminBasePath + "/notification" },
    profile: {
      key: uatAdminBasePath + "/profile",
    },
  },
  MARKETING: {
    notification: { key: marketingBasePath + "/notification" },
    marketingDashboard: {
      key: marketingBasePath + "/",
    },
    validation: {
      key: marketingBasePath + "/validation",
    },
    commissionAndRequirements: {
      key: marketingBasePath + "/validation/commission-and-requirements",
    },
    viewProductProposal: {
      key: marketingBasePath + "/validation/commission-and-requirements/view/:id",
      parse: (id: string) => marketingBasePath + `/validation/commission-and-requirements/view/${id}`,
    },
    shares: {
      key: marketingBasePath + "/validation/shares",
    },
    profile: {
      key: marketingBasePath + "/profile",
    },
    productProposal: {
      key: marketingBasePath + "/product-proposal",
    },
    viewProductProposalCustomize: {
      key: marketingBasePath + "/product-proposal/:id",
      parse: (id: string) => marketingBasePath + `/product-proposal/${id}`,
    },
    viewProductProposalSales: {
      key: marketingBasePath + "/product-proposal/:id",
      parse: (id: string) => marketingBasePath + `/product-proposal/${id}`,
    },
  },
  SALESEXECUTIVEASSISTANT: {
    notification: { key: salesExecutiveAssistantBasePath + "/notification" },
    salesExecutiveAssistantDashboard: {
      key: salesExecutiveAssistantBasePath + "/dashboard",
    },
    notarization: {
      key: salesExecutiveAssistantBasePath + "/product-proposals-notarization",
    },
    partnershipAgreementNotary: {
      key: salesExecutiveAssistantBasePath + "/product-proposals-notarization/partnership-agreement/:id",
      parse: (id: string) => salesExecutiveAssistantBasePath + `/product-proposals-notarization/partnership-agreement/${id}`,
    },
    productProposal: {
      key: salesExecutiveAssistantBasePath + "/product-proposal",
    },
    profile: {
      key: salesExecutiveAssistantBasePath + "/profile",
    },
  },

  ACTUARY: {
    notification: { key: actuaryBasePath + "/notification" },
    actuaryDashboard: {
      key: actuaryBasePath + "/dashboard",
    },
    AER: {
      key: actuaryBasePath + "/AER",
    },
    GYRT: {
      key: actuaryBasePath + "/AER/gyrt",
    },
    reviewAerGYRT: {
      key: actuaryBasePath + "/GYRT/review-aer",
    },
    createAerGYRT: {
      key: actuaryBasePath + "/AER/create-aer",
    },
    viewAerGYRT: {
      key: actuaryBasePath + "/GYRT/view-aer",
    },
    viewQoutationRequestGYRT: {
      key: actuaryBasePath + "/GYRT/view-qoutation-request/:id",
      parse: (id: string) => actuaryBasePath + `/GYRT/view-qoutation-request/${id}`,
    },
    signatoryGYRT: {
      key: actuaryBasePath + "/GYRT/create-aer/signatory",
    },
    benefits: {
      key: actuaryBasePath + "/product-utilities/benefits",
    },
    FIP: {
      key: actuaryBasePath + "/FIP",
    },
    createAerFIP: {
      key: actuaryBasePath + "/FIP/create-aer",
    },
    viewAerFIP: {
      key: actuaryBasePath + "/FIP/view-aer",
    },
    reviewAerFIP: {
      key: actuaryBasePath + "/FIP/review-aer",
    },
    viewQoutationRequestFIP: {
      key: actuaryBasePath + "/FIP/view-qoutation-request/:id",
      parse: (id: string) => actuaryBasePath + `/FIP/view-qoutation-request/${id}`,
    },
    signatoryFIP: {
      key: actuaryBasePath + "/FIP/create-aer/signatory",
    },
    CLSP: {
      key: actuaryBasePath + "/AER/CLSP",
    },
    viewCLSPAER: {
      key: actuaryBasePath + "/CLSP/view-aer",
    },
    viewQuotationRequestCLSP: {
      key: actuaryBasePath + "/CLSP/view-quotation-request",
    },
    createCLSPAER: {
      key: actuaryBasePath + "/CLSP/create-aer-qr",
    },
    createCLSPSignatory: {
      key: actuaryBasePath + "/clsp-signatory",
    },
    reviewCLSP: {
      key: actuaryBasePath + "/clsp/review",
    },
    utilities: {
      key: actuaryBasePath + "/utilities",
    },
    mortalityRate: {
      key: actuaryBasePath + "/utilities/mortality-rate",
    },
    adminExpense: {
      key: actuaryBasePath + "/utilities/admin-expense",
    },
    picRate: {
      key: actuaryBasePath + "/utilities/pic-rate",
    },
    riskPremiumRate: {
      key: actuaryBasePath + "/utilities/risk-premium-rate",
    },
    defaultNumberOfClaims: {
      key: actuaryBasePath + "/utilities/default-number-of-claims",
    },
    benefitRate: {
      key: actuaryBasePath + "/utilities/benefit-rate",
    },
    CLPP: {
      key: actuaryBasePath + "/AER/CLPP",
    },
    viewQuotationRequestCLPP: {
      key: actuaryBasePath + "/CLPP/view-quotation-request",
    },
    createClppAER: {
      key: actuaryBasePath + "/CLPP/create-aer",
    },
    createCLPPSignatory: {
      key: actuaryBasePath + "/clpp-signatory",
    },
    reviewCLPP: {
      key: actuaryBasePath + "/clpp/review",
    },
    profile: {
      key: actuaryBasePath + "/profile",
    },

    GYRTMortalities: {
      key: actuaryBasePath + "/utilities/mortality-rate/gyrt-mortality-rates/:type",
      parse: (type: string) => actuaryBasePath + `/utilities/mortality-rate/gyrt-mortality-rates/${type}`,
    },
    CLSPMortalies: {
      key: actuaryBasePath + "/utilities/mortality-rate/clsp-mortality-rates/:type",
      parse: (type: string) => actuaryBasePath + `/utilities/mortality-rate/clsp-mortality-rates/${type}`,
    },
    PICMortalities: {
      key: actuaryBasePath + "/utilities/mortality-rate/pic-mortality-rates",
    },

    clppRate: {
      key: actuaryBasePath + "/utilities/clpp-rate",
    },
    actuaryUtilities: {
      key: actuaryBasePath + "/utilities/actuary",
    },
    requestDashboard: {
      key: actuaryBasePath + "/request-dashboard",
    },
    requestForm: {
      key: actuaryBasePath + "/request-form",
    },
    viewRequestForm: {
      key: actuaryBasePath + "/request-form/:id",
      parse: (id: string) => actuaryBasePath + `/request-form/${id}`,
    },
    productProposal: {
      key: actuaryBasePath + "/product-proposal",
    },
    viewProductProposalCustomize: {
      key: actuaryBasePath + "/product-proposal/:id",
      parse: (id: string) => actuaryBasePath + `/product-proposal/${id}`,
    },
    viewProductProposal: {
      key: actuaryBasePath + "/product-proposal/:id",
      parse: (id: string) => actuaryBasePath + `/product-proposal/${id}`,
    },
  },
  CASHIER: {
    notification: { key: cashierBasePath + "/notification" },
    actuaryDashboard: {
      key: cashierBasePath + "/dashboard",
    },
    shares: {
      key: cashierBasePath + "/shares",
    },
    profile: {
      key: cashierBasePath + "/profile",
    },
  },

  SALES: {
    notification: { key: salesBasePath + "/notification" },
    salesDashboard: {
      key: salesBasePath + "/dashboard",
    },
    shares: {
      key: salesBasePath + "/shares",
    },
    quotations: {
      key: salesBasePath + "/quotations",
    },
    quotationGyrtAerView: {
      key: salesBasePath + "/quotations/aer-view/gyrt/:id",
      parse: (id: string) => salesBasePath + "/quotations/aer-view/gyrt/" + `${id}`,
    },
    quotationGyrtQuotationView: {
      key: salesBasePath + "/quotations/quotation-view/gyrt/:id",
      parse: (id: string) => salesBasePath + "/quotations/quotation-view/gyrt/" + `${id}`,
    },

    quotationClppAerView: {
      key: salesBasePath + "/quotations/aer-view/clpp/:id",
      parse: (id: string) => salesBasePath + "/quotations/aer-view/clpp/" + `${id}`,
    },
    quotationClppQuotationView: {
      key: salesBasePath + "/quotations/quotation-view/clpp/:id",
      parse: (id: string) => salesBasePath + "/quotations/quotation-view/clpp/" + `${id}`,
    },

    quotationClspAerView: {
      key: salesBasePath + "/quotations/aer-view/clsp/:id",
      parse: (id: string) => salesBasePath + "/quotations/aer-view/clsp/" + `${id}`,
    },
    quotationClspQuotationView: {
      key: salesBasePath + "/quotations/quotation-view/clsp/:id",
      parse: (id: string) => salesBasePath + "/quotations/quotation-view/clsp/" + `${id}`,
    },

    gyrtQuotation: {
      key: salesBasePath + "/quotations/gyrt/:id",
      parse: (id: string) => salesBasePath + "/quotations/gyrt/" + `${id}`,
    },
    clppQuotation: {
      key: salesBasePath + "/quotations/clpp/:id",
      parse: (id: string) => salesBasePath + "/quotations/clpp/" + `${id}`,
    },
    clspQuotation: {
      key: salesBasePath + "/quotations/clsp/:id",
      parse: (id: string) => salesBasePath + "/quotations/clsp/" + `${id}`,
    },
    fipQuotation: {
      key: salesBasePath + "/quotations/fip/:id",
      parse: (id: string) => salesBasePath + "/quotations/fip/" + `${id}`,
    },
    productProposal: {
      key: salesBasePath + "/product-proposal",
    },
    createProductProposal: {
      key: salesBasePath + "/product-proposal/create",
    },
    editProductProposal: {
      key: salesBasePath + "/product-proposal/edit/:id",
      parse: (id: string) => salesBasePath + `/product-proposal/edit/${id}`,
    },
    quotationFipAerView: {
      key: salesBasePath + "/quotations/aer-view/fip/:id",
      parse: (id: string) => salesBasePath + "/quotations/aer-view/fip/" + `${id}`,
    },
    viewProductProposal: {
      key: salesBasePath + "/product-proposal/:id",
      parse: (id: string) => salesBasePath + `/product-proposal/${id}`,
    },
    requestDashboard: {
      key: salesBasePath + "/request-dashboard",
    },
    requestForm: {
      key: salesBasePath + "/request-form",
    },
    viewRequestForm: {
      key: salesBasePath + "/request-form/:id",
      parse: (id: string) => salesBasePath + `/request-form/${id}`,
    },
    profile: {
      key: salesBasePath + "/profile",
    },
  },

  TREASURY: {
    notification: { key: treasuryBasePath + "/notification" },
    treasuryDashboard: {
      key: treasuryBasePath + "/dashboard",
    },
    shares: {
      key: treasuryBasePath + "/shares",
    },
    newForms: {
      key: treasuryBasePath + "/new-forms",
    },
    viewForm: {
      key: treasuryBasePath + "/new-forms/view/:id",
      parse: (id: string) => treasuryBasePath + `/new-forms/view/${id}`,
    },
    requestPads: {
      key: treasuryBasePath + "/request-pads",
    },
    requestPadForm: {
      key: treasuryBasePath + "/request-pads/view/:id",
      parse: (id: string) => treasuryBasePath + `/request-pads/view/${id}`,
    },
    profile: {
      key: treasuryBasePath + "/profile",
    },
  },
  COMPLIANCE: {
    notification: { key: complianceBasePath + "/notification" },
    complianceDashboard: {
      key: complianceBasePath + "/dashboard",
    },
    compliance: {
      key: complianceBasePath + "/compliance",
    },
    viewCompliance: {
      key: complianceBasePath + "/compliance/:id",
      parse: (id: string) => complianceBasePath + `/compliance/${id}`,
    },
    profile: {
      key: complianceBasePath + "/profile",
    },
  },
  INCOMINGCASHIER: {
    notification: { key: incomingCashierBasePath + "/notification" },
    incomingCashierDashboard: {
      key: incomingCashierBasePath + "/dashboard",
    },
    profile: {
      key: incomingCashierBasePath + "/profile",
    },
  },
  OUTGOINGCASHIER: {
    notification: { key: outgoingCashierBasePath + "/notification" },
    outgoingCashierDashboard: {
      key: outgoingCashierBasePath + "/dashboard",
    },
    profile: {
      key: outgoingCashierBasePath + "/profile",
    },
  },
  OUTGOINGADMIN: {
    notification: { key: outgoingAdminBasePath + "/notification" },
    outgoingAdminDashboard: {
      key: outgoingAdminBasePath + "/dashboard",
    },
    outgoingAdminNewForms: {
      key: outgoingAdminBasePath + "/new-forms",
    },
    outgoingAdminNewFormsOutgoing: {
      key: outgoingAdminBasePath + "/new-forms/outgoing",
    },
    forOutgoingAdminReceivingForm: {
      key: outgoingAdminBasePath + "/for-receiving-form/:id",
      parse: (id: string) => outgoingAdminBasePath + `/for-receiving-form/${id}`,
    },
    viewOutgoingAdminTransmittal: {
      key: outgoingAdminBasePath + "/new-forms/outgoing/view/:id",
      parse: (id: string) => outgoingAdminBasePath + `/new-forms/outgoing/view/${id}`,
    },
    viewOutgoingAdminTransmittalTrail: {
      key: outgoingAdminBasePath + "/new-forms/outgoing/transmittaltrail/view/:id",
      parse: (id: string) => outgoingAdminBasePath + `/new-forms/outgoing/transmittaltrail/view/${id}`,
    },
    profile: {
      key: outgoingAdminBasePath + "/profile",
    },
  },
  INCOMINGADMIN: {
    notification: { key: incomingAdminBasePath + "/notification" },
    incomingAdminDashboard: {
      key: incomingAdminBasePath + "/dashboard",
    },
    incomingAdminNewForms: {
      key: incomingAdminBasePath + "/new-forms",
    },
    incomingAdminNewFormsIncoming: {
      key: incomingAdminBasePath + "/new-forms/incoming",
    },
    forIncomingAdminReceivingForm: {
      key: incomingAdminBasePath + "/for-receiving-form/:id",
      parse: (id: string) => incomingAdminBasePath + `/for-receiving-form/${id}`,
    },
    viewIncomingAdminTransmittal: {
      key: incomingAdminBasePath + "/new-forms/incoming/view/:id",
      parse: (id: string) => incomingAdminBasePath + `/new-forms/incoming/view/${id}`,
    },
    viewIncomingAdminTransmittalTrail: {
      key: incomingAdminBasePath + "/new-forms/incoming/transmittaltrail/view/:id",
      parse: (id: string) => incomingAdminBasePath + `/new-forms/incoming/transmittaltrail/view/${id}`,
    },
    profile: {
      key: incomingAdminBasePath + "/profile",
    },
  },
  CHIEFCASHIER: {
    notification: { key: chiefCashierBasePath + "/notification" },
    chiefCashierDashboard: {
      key: chiefCashierBasePath + "/dashboard",
    },
    viewNewTransmittalForm: {
      key: chiefCashierBasePath + "/new-forms/outgoing/view",
    },
    viewNewTransmittal: {
      key: chiefCashierBasePath + "/new-forms/outgoing/view/id",
    },
    inventory: {
      key: chiefCashierBasePath + "/inventory",
    },
    inventoryVerifiedList: {
      key: chiefCashierBasePath + "/inventory/verified-list/:id",
    },
    inventoryNewForms: {
      key: chiefCashierBasePath + "/inventory/new-forms",
    },
    inventoryUsedForms: {
      key: chiefCashierBasePath + "/inventory/used-forms",
    },
    verificationList: {
      key: chiefCashierBasePath + "/verification-list",
    },
    verificationListForm: {
      key: chiefCashierBasePath + "/verification-list/form/:id",
      parse: (id: string) => chiefCashierBasePath + `/verification-list/form/${id}`,
    },
    statusTracking: {
      key: chiefCashierBasePath + "/status-tracking",
    },
    statusTrackingView: {
      key: chiefCashierBasePath + "/status-tracking/view",
    },
    seriesAssignment: {
      key: chiefCashierBasePath + "/series-assignment",
    },
    seriesAssignmentForm: {
      key: chiefCashierBasePath + "/series-assignment/form/:id",
      parse: (id: string) => chiefCashierBasePath + `/series-assignment/form/${id}`,
    },
    viewSeriesAssignment: {
      key: chiefCashierBasePath + "/series-assignment/view/:id",
      parse: (id: string) => chiefCashierBasePath + `/series-assignment/view/${id}`,
    },
    usedForms: {
      key: chiefCashierBasePath + "/used-forms",
    },
    forReturnedReceivingForm: {
      key: chiefCashierBasePath + "/receive-returned-form/:id",
      parse: (id: string) => chiefCashierBasePath + `/receive-returned-form/${id}`,
    },
    requestPads: {
      key: chiefCashierBasePath + "/request-pads",
    },
    viewRequestPadForm: {
      key: chiefCashierBasePath + "/request-pads/view/:id",
      parse: (id: string) => chiefCashierBasePath + `/request-pads/view/${id}`,
    },
    profile: {
      key: chiefCashierBasePath + "/profile",
    },
  },
  CLIFSAADMIN: {
    notification: { key: clifsaAdminBasePath + "/notification" },
    clifsaAdminDashboard: {
      key: clifsaAdminBasePath + "/dashboard",
    },
    clifsaAdminNewForm: {
      key: clifsaAdminBasePath + "/new-forms",
    },
    clifsaAdminForReceivingForms: {
      key: clifsaAdminBasePath + "/for-receiving-forms",
    },
    viewForReceivingForm: {
      key: clifsaAdminBasePath + "/for-receiving-forms/view/:id",
      parse: (id: string) => clifsaAdminBasePath + `/for-receiving-forms/view/${id}`,
    },
    viewTransmittalForm: {
      key: clifsaAdminBasePath + "/new-forms/view/:id",
      parse: (id: string) => clifsaAdminBasePath + `/new-forms/view/${id}`,
    },
    viewReleasedForm: {
      key: clifsaAdminBasePath + "/released-form/:id",
      parse: (id: string) => clifsaAdminBasePath + `/released-form/${id}`,
    },
    usedForms: {
      key: clifsaAdminBasePath + "/used-forms",
    },
    viewReturnedReceivingForm: {
      key: clifsaAdminBasePath + "/for-receiving-forms/view/:id",
      parse: (id: string) => clifsaAdminBasePath + `/for-receiving-forms/view/${id}`,
    },
    viewReturnTransmittalForm: {
      key: clifsaAdminBasePath + "/used-forms/view/:id",
      parse: (id: string) => clifsaAdminBasePath + `/used-forms/view/${id}`,
    },
    viewReturnedForm: {
      key: clifsaAdminBasePath + "/returned-forms/:id",
      parse: (id: string) => clifsaAdminBasePath + `/returned-forms/${id}`,
    },
    requestPads: {
      key: clifsaAdminBasePath + "/requests-pads",
    },
    requestPadForm: {
      key: clifsaAdminBasePath + "/request-pads/view/:id",
      parse: (id: string) => clifsaAdminBasePath + `/request-pads/view/${id}`,
    },
    profile: {
      key: clifsaAdminBasePath + "/profile",
    },
  },
  GAM: {
    notification: { key: gamBasePath + "/notification" },
    gamDashboard: {
      key: gamBasePath + "/dashboard",
    },
    gamForReceivingForms: {
      key: gamBasePath + "/for-receiving-forms",
    },
    gamAdminNewForm: {
      key: gamBasePath + "/new-forms",
    },
    gamInventory: {
      key: gamBasePath + "/inventory",
    },
    viewForReceivingForm: {
      key: gamBasePath + "/for-receiving-forms/view/:id",
      parse: (id: string) => gamBasePath + `/for-receiving-forms/view/${id}`,
    },
    viewFormReceiving: {
      key: gamBasePath + "/new-forms/view/:id",
      parse: (id: string) => gamBasePath + `/new-forms/view/${id}`,
    },
    viewReleasedForm: {
      key: gamBasePath + "/released-form/:id",
      parse: (id: string) => gamBasePath + `/released-form/${id}`,
    },
    requestPads: {
      key: gamBasePath + "/request-pads",
    },
    transmittalReturnedForm: {
      key: gamBasePath + "/return-transmittal",
    },
    viewPrTable: {
      key: gamBasePath + "/pr-table/:id",
      parse: (id: string) => gamBasePath + `/pr-table/${id}`,
    },
    issuePRForm: {
      key: gamBasePath + "/issue-pr-form/:id",
      parse: (id: string) => gamBasePath + `/issue-pr-form/${id}`,
    },
    viewPR: {
      key: gamBasePath + "/view-pr/:id",
      parse: (id: string) => gamBasePath + `/view-pr/${id}`,
    },
    profile: {
      key: gamBasePath + "/profile",
    },
  },
  INCOMINGOUTGOINGCASHIER: {
    notification: { key: incomingOutgoingCashierBasePath + "/notification" },
    incomingOutgoingCashierDashboard: {
      key: incomingOutgoingCashierBasePath + "/dashboard",
    },
    newForms: {
      key: incomingOutgoingCashierBasePath + "/new-forms",
    },
    newFormsIncoming: {
      key: incomingOutgoingCashierBasePath + "/new-forms/incoming",
    },
    viewNewForm: {
      key: incomingOutgoingCashierBasePath + "/new-forms/incoming/view/:id",
      parse: (id: string) => incomingOutgoingCashierBasePath + `/new-forms/incoming/view/${id}`,
    },
    newFormsOutgoing: {
      key: incomingOutgoingCashierBasePath + "/new-forms/outgoing",
    },
    viewOutgoingForm: {
      key: incomingOutgoingCashierBasePath + "/new-forms/outgoing/view/:id",
      parse: (id: string) => incomingOutgoingCashierBasePath + `/new-forms/outgoing/view/${id}`,
    },
    forReceivingForm: {
      key: incomingOutgoingCashierBasePath + "/for-receiving-form/:id",
      parse: (id: string) => incomingOutgoingCashierBasePath + `/for-receiving-form/${id}`,
    },
    releasedForms: {
      key: incomingOutgoingCashierBasePath + "/released-forms/:id",
      parse: (id: string) => incomingOutgoingCashierBasePath + `/released-forms/${id}`,
    },
    usedForms: {
      key: incomingOutgoingCashierBasePath + "/used-forms",
    },
    forReturnedReceivingForm: {
      key: incomingOutgoingCashierBasePath + "/receive-returned-form/:id",
      parse: (id: string) => incomingOutgoingCashierBasePath + `/receive-returned-form/${id}`,
    },
    viewIOCTransmittal: {
      key: incomingOutgoingCashierBasePath + "/used-forms/return/view/:id",
      parse: (id: string) => incomingOutgoingCashierBasePath + `/used-forms/return/view/${id}`,
    },
    viewIOCTransmittalTrail: {
      key: incomingOutgoingCashierBasePath + "/used-forms/transmittaltrail/view/:id",
      parse: (id: string) => incomingOutgoingCashierBasePath + `/used-forms/transmittaltrail/view/${id}`,
    },
    inventory: {
      key: incomingOutgoingCashierBasePath + "/inventory",
    },
    verifiedFormsInventory: {
      key: incomingOutgoingCashierBasePath + "/inventory/verified-forms",
    },
    newFormsInventory: {
      key: incomingOutgoingCashierBasePath + "/inventory/new-forms",
    },
    usedFormInventory: {
      key: incomingOutgoingCashierBasePath + "/inventory/used-forms",
    },
    profile: {
      key: incomingOutgoingCashierBasePath + "/profile",
    },
  },
  UNDERWRITING: {
    notification: { key: underwritingBasePath + "/notification" },
    underwritingDashboard: {
      key: underwritingBasePath + "/dashboard",
    },
    UnderwritingProductProposalApproval: {
      key: underwritingBasePath + "/product-proposal-approvals/underwriting",
    },
    viewUnderwritingProductProposalApproval: {
      key: underwritingBasePath + "/product-proposals-approvals/proposal/underwriting/:id",
      parse: (id: string) => underwritingBasePath + `/product-proposals-approvals/proposal/underwriting/${id}`,
    },
    profile: {
      key: underwritingBasePath + "/profile",
    },
  },
  CLAIMS: {
    notification: { key: claimsBasePath + "/notification" },
    claimsDashboard: {
      key: claimsBasePath + "/dashboard",
    },
    claimsProductProposalApproval: {
      key: claimsBasePath + "/product-proposal-approvals/claims",
    },
    viewClaimsProductProposalApproval: {
      key: claimsBasePath + "/product-proposals-approvals/proposal/claims/:id",
      parse: (id: string) => claimsBasePath + `/product-proposals-approvals/proposal/claims/${id}`,
    },
    requestDashboard: {
      key: claimsBasePath + "/request-dashboard",
    },
    requestForm: {
      key: claimsBasePath + "/request-form",
    },
    viewRequestForm: {
      key: claimsBasePath + "/request-form/:id",
      parse: (id: string) => claimsBasePath + `/request-form/${id}`,
    },
    profile: {
      key: claimsBasePath + "/profile",
    },
  },
  ACTUARYANALYST1: {
    notification: { key: actuaryAnalyst1BasePath + "/notification" },
    dashboard: {
      key: actuaryAnalyst1BasePath + "/dashboard",
    },

    quotationFipAerView: {
      key: actuaryAnalyst1BasePath + "/quotations/aer-view/fip/:id",
      parse: (id: string) => actuaryAnalyst1BasePath + "/quotations/aer-view/fip/" + `${id}`,
    },

    quotationClspQuotationView: {
      key: actuaryAnalyst1BasePath + "/quotations/quotation-view/clsp/:id",
      parse: (id: string) => actuaryAnalyst1BasePath + "/quotations/quotation-view/clsp/" + `${id}`,
    },
    quotationClppQuotationView: {
      key: actuaryAnalyst1BasePath + "/quotations/quotation-view/clpp/:id",
      parse: (id: string) => actuaryAnalyst1BasePath + "/quotations/quotation-view/clpp/" + `${id}`,
    },
    quotationGyrtQuotationView: {
      key: actuaryAnalyst1BasePath + "/quotations/quotation-view/gyrt/:id",
      parse: (id: string) => actuaryAnalyst1BasePath + "/quotations/quotation-view/gyrt/" + `${id}`,
    },
    myApprovals: {
      key: actuaryAnalyst1BasePath + "/my-approvals",
    },
    aerApproval: {
      key: actuaryAnalyst1BasePath + "/my-approvals/aer-approvals",
    },
    profile: {
      key: actuaryAnalyst1BasePath + "/profile",
    },
  },
  ACTUARYASSISTANT1: {
    notification: { key: actuaryAssistant1BasePath + "/notification" },
    dashboard: {
      key: actuaryAssistant1BasePath + "/dashboard",
    },
    quotationFipAerView: {
      key: actuaryAssistant1BasePath + "/quotations/aer-view/fip/:id",
      parse: (id: string) => actuaryAssistant1BasePath + "/quotations/aer-view/fip/" + `${id}`,
    },

    quotationClspQuotationView: {
      key: actuaryAssistant1BasePath + "/quotations/quotation-view/clsp/:id",
      parse: (id: string) => actuaryAssistant1BasePath + "/quotations/quotation-view/clsp/" + `${id}`,
    },
    quotationClppQuotationView: {
      key: actuaryAssistant1BasePath + "/quotations/quotation-view/clpp/:id",
      parse: (id: string) => actuaryAssistant1BasePath + "/quotations/quotation-view/clpp/" + `${id}`,
    },
    quotationGyrtQuotationView: {
      key: actuaryAssistant1BasePath + "/quotations/quotation-view/gyrt/:id",
      parse: (id: string) => actuaryAssistant1BasePath + "/quotations/quotation-view/gyrt/" + `${id}`,
    },
    myApprovals: {
      key: actuaryAssistant1BasePath + "/my-approvals",
    },
    aerApproval: {
      key: actuaryAssistant1BasePath + "/my-approvals/aer-approvals",
    },
    profile: {
      key: actuaryAssistant1BasePath + "/profile",
    },
  },

  ACTUARYMANAGER: {
    notification: { key: actuaryManagerBasePath + "/notification" },
    dashboard: {
      key: actuaryManagerBasePath + "/dashboard",
    },
    quotationFipAerView: {
      key: actuaryManagerBasePath + "/quotations/aer-view/fip/:id",
      parse: (id: string) => actuaryManagerBasePath + "/quotations/aer-view/fip/" + `${id}`,
    },

    quotationClspQuotationView: {
      key: actuaryManagerBasePath + "/quotations/quotation-view/clsp/:id",
      parse: (id: string) => actuaryManagerBasePath + "/quotations/quotation-view/clsp/" + `${id}`,
    },
    quotationClppQuotationView: {
      key: actuaryManagerBasePath + "/quotations/quotation-view/clpp/:id",
      parse: (id: string) => actuaryManagerBasePath + "/quotations/quotation-view/clpp/" + `${id}`,
    },
    quotationGyrtQuotationView: {
      key: actuaryManagerBasePath + "/quotations/quotation-view/gyrt/:id",
      parse: (id: string) => actuaryManagerBasePath + "/quotations/quotation-view/gyrt/" + `${id}`,
    },
    myApprovals: {
      key: actuaryManagerBasePath + "/my-approvals",
    },
    aerApproval: {
      key: actuaryManagerBasePath + "/my-approvals/aer-approvals",
    },
    requestDashboard: {
      key: actuaryManagerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: actuaryManagerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: actuaryManagerBasePath + "/request-form/:id",
      parse: (id: string) => actuaryManagerBasePath + `/request-form/${id}`,
    },
    profile: {
      key: actuaryManagerBasePath + "/profile",
    },
  },
  AVPFORLIFEANDNONLIFE: {
    notification: { key: avpLifeNonLifeManagerBasePath + "/notification" },
    dashboard: {
      key: avpLifeNonLifeManagerBasePath + "/dashboard",
    },

    quotationFipAerView: {
      key: avpLifeNonLifeManagerBasePath + "/quotations/aer-view/fip/:id",
      parse: (id: string) => avpLifeNonLifeManagerBasePath + "/quotations/aer-view/fip/" + `${id}`,
    },

    quotationClspQuotationView: {
      key: avpLifeNonLifeManagerBasePath + "/quotations/quotation-view/clsp/:id",
      parse: (id: string) => avpLifeNonLifeManagerBasePath + "/quotations/quotation-view/clsp/" + `${id}`,
    },
    quotationClppQuotationView: {
      key: avpLifeNonLifeManagerBasePath + "/quotations/quotation-view/clpp/:id",
      parse: (id: string) => avpLifeNonLifeManagerBasePath + "/quotations/quotation-view/clpp/" + `${id}`,
    },
    quotationGyrtQuotationView: {
      key: avpLifeNonLifeManagerBasePath + "/quotations/quotation-view/gyrt/:id",
      parse: (id: string) => avpLifeNonLifeManagerBasePath + "/quotations/quotation-view/gyrt/" + `${id}`,
    },
    myApprovals: {
      key: avpLifeNonLifeManagerBasePath + "/my-approvals",
    },
    aerApproval: {
      key: avpLifeNonLifeManagerBasePath + "/my-approvals/aer-approvals",
    },
    requestDashboard: {
      key: avpLifeNonLifeManagerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: avpLifeNonLifeManagerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: avpLifeNonLifeManagerBasePath + "/request-form/:id",
      parse: (id: string) => avpLifeNonLifeManagerBasePath + `/request-form/${id}`,
    },
    profile: {
      key: avpLifeNonLifeManagerBasePath + "/profile",
    },
  },
  RESEARCHANDDEVELOPMENT: {
    dashboard: {
      key: researchAndDevelopmentBasePath + "/dashboard",
    },
    products: {
      key: researchAndDevelopmentBasePath + "/products",
    },
    revisions: {
      key: researchAndDevelopmentBasePath + "/products/:productid/revisions",
      parse: (pid: string) => researchAndDevelopmentBasePath + `/products/${pid}/revisions`,
    },
    reviewRevisions: {
      key: researchAndDevelopmentBasePath + "/products/:productid/revision/:revisionid",
      parse: (pid: string, rid: string) => researchAndDevelopmentBasePath + `/products/${pid}/revision/${rid}`,
    },
    editProductDetails: {
      key: researchAndDevelopmentBasePath + "/product/details/:id",
      parse: (id: string) => researchAndDevelopmentBasePath + `/product/details/${id}`,
    },
    guidelines: {
      key: researchAndDevelopmentBasePath + "/products/guidelines",
    },
    createGuidelines: {
      key: researchAndDevelopmentBasePath + "/products/guidelines/create",
    },
    editGuidelines: {
      key: researchAndDevelopmentBasePath + "/products/guidelines/edit/:id",
      parse: (id: string) => researchAndDevelopmentBasePath + `/products/guidelines/edit/${id}`,
    },
    editRevision: {
      key: researchAndDevelopmentBasePath + "/products/revisions/edit/:id",
      parse: (id: string) => researchAndDevelopmentBasePath + `/products/revisions/edit/${id}`,
    },
    cloneProductGuideline: {
      key: researchAndDevelopmentBasePath + "/products/guidelines/clone",
    },
    cloneProduct: {
      key: researchAndDevelopmentBasePath + "/products/clone",
    },
    reviewProductRevisionsFromEmail: {
      key: researchAndDevelopmentBasePath + "/products/:productid/revision/:revisionid/review",
      parse: (pid: string, rid: string) => researchAndDevelopmentBasePath + `/products/${pid}/revision/${rid}/review`,
    },
    notification: { key: researchAndDevelopmentBasePath + "/notification" },
    profile: {
      key: researchAndDevelopmentBasePath + "/profile",
    },
    productProposal: {
      key: researchAndDevelopmentBasePath + "/product-proposal",
    },
    viewProductProposalCustomize: {
      key: researchAndDevelopmentBasePath + "/product-proposal/:id",
      parse: (id: string) => researchAndDevelopmentBasePath + `/product-proposal/${id}`,
    },
    viewProductProposal: {
      key: researchAndDevelopmentBasePath + "/product-proposal/:id",
      parse: (id: string) => researchAndDevelopmentBasePath + `/product-proposal/${id}`,
    },
  },
  CLAIMSMANAGER: {
    dashboard: {
      key: claimsManagerBasePath + "/dashboard",
    },
    requestDashboard: {
      key: claimsManagerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: claimsManagerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: claimsManagerBasePath + "/request-form/:id",
      parse: (id: string) => claimsManagerBasePath + `/request-form/${id}`,
    },
    notification: { key: claimsManagerBasePath + "/notification" },
    profile: {
      key: claimsManagerBasePath + "/profile",
    },
  },

  VICEPRESIDENTFORSALES: {
    dashboard: {
      key: vicePresidentSalesBasePath + "/dashboard",
    },
    myApprovals: {
      key: vicePresidentSalesBasePath + "/my-approvals",
    },
    commissionAndRequirements: {
      key: vicePresidentSalesBasePath + "/my-approvals/validation/commission-and-requirements",
    },
    viewProductProposalSignatory: {
      key: vicePresidentSalesBasePath + "/approvals/validation/commission-and-requirements/view/:id",
      parse: (id: string) => vicePresidentSalesBasePath + `/approvals/validation/commission-and-requirements/view/${id}`,
    },
    notification: { key: vicePresidentSalesBasePath + "/notification" },
    profile: {
      key: vicePresidentSalesBasePath + "/profile",
    },
  },
  VICEPRESIDENTFOROPERATION: {
    dashboard: {
      key: vicePresidentOperationsBasePath + "/dashboard",
    },
    myApprovals: {
      key: vicePresidentOperationsBasePath + "/my-approvals",
    },
    commissionAndRequirements: {
      key: vicePresidentOperationsBasePath + "/my-approvals/validation/commission-and-requirements",
    },
    viewProductProposalSignatory: {
      key: vicePresidentOperationsBasePath + "/approvals/validation/commission-and-requirements/view/:id",
      parse: (id: string) => vicePresidentOperationsBasePath + `/approvals/validation/commission-and-requirements/view/${id}`,
    },
    notification: { key: vicePresidentOperationsBasePath + "/notification" },
    profile: {
      key: vicePresidentOperationsBasePath + "/profile",
    },
  },
  PRESIDENTANDCEO: {
    dashboard: {
      key: presidentCeoBasePath + "/dashboard",
    },
    myApprovals: {
      key: presidentCeoBasePath + "/my-approvals",
    },
    commissionAndRequirements: {
      key: presidentCeoBasePath + "/my-approvals/validation/commission-and-requirements",
    },
    viewProductProposalSignatory: {
      key: presidentCeoBasePath + "/approvals/validation/commission-and-requirements/view/:id",
      parse: (id: string) => presidentCeoBasePath + `/approvals/validation/commission-and-requirements/view/${id}`,
    },
    requestDashboard: {
      key: presidentCeoBasePath + "/request-dashboard",
    },
    requestForm: {
      key: presidentCeoBasePath + "/request-form",
    },
    viewRequestForm: {
      key: presidentCeoBasePath + "/request-form/:id",
      parse: (id: string) => presidentCeoBasePath + `/request-form/${id}`,
    },
    notification: { key: presidentCeoBasePath + "/notification" },
    profile: {
      key: presidentCeoBasePath + "/profile",
    },
  },
  AREASALESMANAGER: {
    dashboard: {
      key: areaSalesManagerBasePath + "/dashboard",
    },
    myApprovals: {
      key: areaSalesManagerBasePath + "/my-approvals",
    },
    commissionAndRequirements: {
      key: areaSalesManagerBasePath + "/my-approvals/validation/commission-and-requirements",
    },
    viewProductProposalSignatory: {
      key: areaSalesManagerBasePath + "/approvals/validation/commission-and-requirements/view/:id",
      parse: (id: string) => areaSalesManagerBasePath + `/approvals/validation/commission-and-requirements/view/${id}`,
    },
    notification: { key: areaSalesManagerBasePath + "/notification" },
    profile: {
      key: areaSalesManagerBasePath + "/profile",
    },
  },
  INFRASTRUCTUREOFFICER: {
    dashboard: {
      key: infraOfficerBasePath + "/dashboard",
    },
    requestDashboard: {
      key: infraOfficerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: infraOfficerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: infraOfficerBasePath + "/request-form/:id",
      parse: (id: string) => infraOfficerBasePath + `/request-form/${id}`,
    },
    notification: { key: infraOfficerBasePath + "/notification" },
    profile: {
      key: infraOfficerBasePath + "/profile",
    },
  },
  ACCOUNTING: {
    dashboard: {
      key: accountingBasePath + "/dashboard",
    },
    requestDashboard: {
      key: accountingBasePath + "/request-dashboard",
    },
    requestForm: {
      key: accountingBasePath + "/request-form",
    },
    viewRequestForm: {
      key: accountingBasePath + "/request-form/:id",
      parse: (id: string) => accountingBasePath + `/request-form/${id}`,
    },
    notification: { key: accountingBasePath + "/notification" },
    profile: {
      key: accountingBasePath + "/profile",
    },
  },
};
